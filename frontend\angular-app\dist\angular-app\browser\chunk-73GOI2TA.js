var vg=Object.defineProperty,Dg=Object.defineProperties;var Eg=Object.getOwnPropertyDescriptors;var Zu=Object.getOwnPropertySymbols;var Ig=Object.prototype.hasOwnProperty,wg=Object.prototype.propertyIsEnumerable;var Yu=(e,t,n)=>t in e?vg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,y=(e,t)=>{for(var n in t||={})Ig.call(t,n)&&Yu(e,n,t[n]);if(Zu)for(var n of Zu(t))wg.call(t,n)&&Yu(e,n,t[n]);return e},j=(e,t)=>Dg(e,Eg(t));var bs;function Ts(){return bs}function $e(e){let t=bs;return bs=e,t}var Cg=Symbol("NotFound"),eo=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function en(e){return e===Cg||e?.name==="\u0275NotFound"}function Qu(e,t){return Object.is(e,t)}var W=null,to=!1,Ss=1;var no=Symbol("SIGNAL");function R(e){let t=W;return W=e,t}function Ms(){return W}var ro={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Ns(e){if(to)throw new Error("");if(W===null)return;W.consumerOnSignalRead(e);let t=W.nextProducerIndex++;if(ao(W),t<W.producerNode.length&&W.producerNode[t]!==e&&$n(W)){let n=W.producerNode[t];so(n,W.producerIndexOfThis[t])}W.producerNode[t]!==e&&(W.producerNode[t]=e,W.producerIndexOfThis[t]=$n(W)?el(e,W,t):0),W.producerLastReadVersion[t]=e.version}function Ku(){Ss++}function Ju(e){if(!($n(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ss)){if(!e.producerMustRecompute(e)&&!io(e)){_s(e);return}e.producerRecomputeValue(e),_s(e)}}function Rs(e){if(e.liveConsumerNode===void 0)return;let t=to;to=!0;try{for(let n of e.liveConsumerNode)n.dirty||bg(n)}finally{to=t}}function Xu(){return W?.consumerAllowSignalWrites!==!1}function bg(e){e.dirty=!0,Rs(e),e.consumerMarkedDirty?.(e)}function _s(e){e.dirty=!1,e.lastCleanEpoch=Ss}function oo(e){return e&&(e.nextProducerIndex=0),R(e)}function As(e,t){if(R(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if($n(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)so(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function io(e){ao(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ju(n),r!==n.version))return!0}return!1}function xs(e){if(ao(e),$n(e))for(let t=0;t<e.producerNode.length;t++)so(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function el(e,t,n){if(tl(e),e.liveConsumerNode.length===0&&nl(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=el(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function so(e,t){if(tl(e),e.liveConsumerNode.length===1&&nl(e))for(let r=0;r<e.producerNode.length;r++)so(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];ao(o),o.producerIndexOfThis[r]=t}}function $n(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ao(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function tl(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function nl(e){return e.producerNode!==void 0}function Tg(){throw new Error}var rl=Tg;function _g(e){rl(e)}function Os(e){rl=e}var Sg=null;function Ps(e,t){Xu()||_g(e),e.equal(e.value,t)||(e.value=t,Mg(e))}var ks=j(y({},ro),{equal:Qu,value:void 0,kind:"signal"});function Mg(e){e.version++,Ku(),Rs(e),Sg?.(e)}function b(e){return typeof e=="function"}function tn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var co=tn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Hn(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var V=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(b(r))try{r()}catch(i){t=i instanceof co?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{ol(i)}catch(s){t=t??[],s instanceof co?t=[...t,...s.errors]:t.push(s)}}if(t)throw new co(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ol(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Hn(n,t)}remove(t){let{_finalizers:n}=this;n&&Hn(n,t),t instanceof e&&t._removeParent(this)}};V.EMPTY=(()=>{let e=new V;return e.closed=!0,e})();var Fs=V.EMPTY;function uo(e){return e instanceof V||e&&"closed"in e&&b(e.remove)&&b(e.add)&&b(e.unsubscribe)}function ol(e){b(e)?e():e.unsubscribe()}var Te={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var nn={setTimeout(e,t,...n){let{delegate:r}=nn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=nn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function lo(e){nn.setTimeout(()=>{let{onUnhandledError:t}=Te;if(t)t(e);else throw e})}function zn(){}var il=Ls("C",void 0,void 0);function sl(e){return Ls("E",void 0,e)}function al(e){return Ls("N",e,void 0)}function Ls(e,t,n){return{kind:e,value:t,error:n}}var It=null;function rn(e){if(Te.useDeprecatedSynchronousErrorHandling){let t=!It;if(t&&(It={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=It;if(It=null,n)throw r}}else e()}function cl(e){Te.useDeprecatedSynchronousErrorHandling&&It&&(It.errorThrown=!0,It.error=e)}var wt=class extends V{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,uo(t)&&t.add(this)):this.destination=Ag}static create(t,n,r){return new on(t,n,r)}next(t){this.isStopped?Bs(al(t),this):this._next(t)}error(t){this.isStopped?Bs(sl(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Bs(il,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Ng=Function.prototype.bind;function js(e,t){return Ng.call(e,t)}var Vs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){fo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){fo(r)}else fo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){fo(n)}}},on=class extends wt{constructor(t,n,r){super();let o;if(b(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Te.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&js(t.next,i),error:t.error&&js(t.error,i),complete:t.complete&&js(t.complete,i)}):o=t}this.destination=new Vs(o)}};function fo(e){Te.useDeprecatedSynchronousErrorHandling?cl(e):lo(e)}function Rg(e){throw e}function Bs(e,t){let{onStoppedNotification:n}=Te;n&&nn.setTimeout(()=>n(e,t))}var Ag={closed:!0,next:zn,error:Rg,complete:zn};var sn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function fe(e){return e}function Us(...e){return $s(e)}function $s(e){return e.length===0?fe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var A=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Og(n)?n:new on(n,r,o);return rn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ul(r),new r((o,i)=>{let s=new on({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[sn](){return this}pipe(...n){return $s(n)(this)}toPromise(n){return n=ul(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function ul(e){var t;return(t=e??Te.Promise)!==null&&t!==void 0?t:Promise}function xg(e){return e&&b(e.next)&&b(e.error)&&b(e.complete)}function Og(e){return e&&e instanceof wt||xg(e)&&uo(e)}function Hs(e){return b(e?.lift)}function P(e){return t=>{if(Hs(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function O(e,t,n,r,o){return new zs(e,t,n,r,o)}var zs=class extends wt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function an(){return P((e,t)=>{let n=null;e._refCount++;let r=O(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var cn=class extends A{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Hs(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new V;let n=this.getSubject();t.add(this.source.subscribe(O(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=V.EMPTY)}return t}refCount(){return an()(this)}};var ll=tn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ee=(()=>{class e extends A{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new ho(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new ll}next(n){rn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){rn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){rn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Fs:(this.currentObservers=null,i.push(n),new V(()=>{this.currentObservers=null,Hn(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new A;return n.source=this,n}}return e.create=(t,n)=>new ho(t,n),e})(),ho=class extends ee{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Fs}};var te=class extends ee{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var se=new A(e=>e.complete());function dl(e){return e&&b(e.schedule)}function fl(e){return e[e.length-1]}function po(e){return b(fl(e))?e.pop():void 0}function ot(e){return dl(fl(e))?e.pop():void 0}function pl(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function hl(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ct(e){return this instanceof Ct?(this.v=e,this):new Ct(e)}function gl(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(m){return new Promise(function(E,_){i.push([f,m,E,_])>1||c(f,m)})},g&&(o[f]=g(o[f])))}function c(f,g){try{u(r[f](g))}catch(m){h(i[0][3],m)}}function u(f){f.value instanceof Ct?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ml(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof hl=="function"?hl(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var go=e=>e&&typeof e.length=="number"&&typeof e!="function";function mo(e){return b(e?.then)}function yo(e){return b(e[sn])}function vo(e){return Symbol.asyncIterator&&b(e?.[Symbol.asyncIterator])}function Do(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Pg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Eo=Pg();function Io(e){return b(e?.[Eo])}function wo(e){return gl(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ct(n.read());if(o)return yield Ct(void 0);yield yield Ct(r)}}finally{n.releaseLock()}})}function Co(e){return b(e?.getReader)}function U(e){if(e instanceof A)return e;if(e!=null){if(yo(e))return kg(e);if(go(e))return Fg(e);if(mo(e))return Lg(e);if(vo(e))return yl(e);if(Io(e))return jg(e);if(Co(e))return Bg(e)}throw Do(e)}function kg(e){return new A(t=>{let n=e[sn]();if(b(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Fg(e){return new A(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Lg(e){return new A(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,lo)})}function jg(e){return new A(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function yl(e){return new A(t=>{Vg(e,t).catch(n=>t.error(n))})}function Bg(e){return yl(wo(e))}function Vg(e,t){var n,r,o,i;return pl(this,void 0,void 0,function*(){try{for(n=ml(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ae(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function bo(e,t=0){return P((n,r)=>{n.subscribe(O(r,o=>ae(r,e,()=>r.next(o),t),()=>ae(r,e,()=>r.complete(),t),o=>ae(r,e,()=>r.error(o),t)))})}function To(e,t=0){return P((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function vl(e,t){return U(e).pipe(To(t),bo(t))}function Dl(e,t){return U(e).pipe(To(t),bo(t))}function El(e,t){return new A(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Il(e,t){return new A(n=>{let r;return ae(n,t,()=>{r=e[Eo](),ae(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>b(r?.return)&&r.return()})}function _o(e,t){if(!e)throw new Error("Iterable cannot be null");return new A(n=>{ae(n,t,()=>{let r=e[Symbol.asyncIterator]();ae(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function wl(e,t){return _o(wo(e),t)}function Cl(e,t){if(e!=null){if(yo(e))return vl(e,t);if(go(e))return El(e,t);if(mo(e))return Dl(e,t);if(vo(e))return _o(e,t);if(Io(e))return Il(e,t);if(Co(e))return wl(e,t)}throw Do(e)}function $(e,t){return t?Cl(e,t):U(e)}function C(...e){let t=ot(e);return $(e,t)}function un(e,t){let n=b(e)?e:()=>e,r=o=>o.error(n());return new A(t?o=>t.schedule(r,0,o):r)}function Gs(e){return!!e&&(e instanceof A||b(e.lift)&&b(e.subscribe))}var He=tn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function M(e,t){return P((n,r)=>{let o=0;n.subscribe(O(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Ug}=Array;function $g(e,t){return Ug(t)?e(...t):e(t)}function So(e){return M(t=>$g(e,t))}var{isArray:Hg}=Array,{getPrototypeOf:zg,prototype:Gg,keys:Wg}=Object;function Mo(e){if(e.length===1){let t=e[0];if(Hg(t))return{args:t,keys:null};if(qg(t)){let n=Wg(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function qg(e){return e&&typeof e=="object"&&zg(e)===Gg}function No(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Ro(...e){let t=ot(e),n=po(e),{args:r,keys:o}=Mo(e);if(r.length===0)return $([],t);let i=new A(Zg(r,t,o?s=>No(o,s):fe));return n?i.pipe(So(n)):i}function Zg(e,t,n=fe){return r=>{bl(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)bl(t,()=>{let u=$(e[c],t),l=!1;u.subscribe(O(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function bl(e,t,n){e?ae(n,e,t):t()}function Tl(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=m=>u<r?g(m):c.push(m),g=m=>{i&&t.next(m),u++;let E=!1;U(n(m,l++)).subscribe(O(t,_=>{o?.(_),i?f(_):t.next(_)},()=>{E=!0},void 0,()=>{if(E)try{for(u--;c.length&&u<r;){let _=c.shift();s?ae(t,s,()=>g(_)):g(_)}h()}catch(_){t.error(_)}}))};return e.subscribe(O(t,f,()=>{d=!0,h()})),()=>{a?.()}}function q(e,t,n=1/0){return b(t)?q((r,o)=>M((i,s)=>t(r,i,o,s))(U(e(r,o))),n):(typeof t=="number"&&(n=t),P((r,o)=>Tl(r,o,e,n)))}function _l(e=1/0){return q(fe,e)}function Sl(){return _l(1)}function ln(...e){return Sl()($(e,ot(e)))}function Gn(e){return new A(t=>{U(e()).subscribe(t)})}function Yg(...e){let t=po(e),{args:n,keys:r}=Mo(e),o=new A(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;U(n[l]).subscribe(O(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?No(r,a):a),i.complete())}))}});return t?o.pipe(So(t)):o}function ce(e,t){return P((n,r)=>{let o=0;n.subscribe(O(r,i=>e.call(t,i,o++)&&r.next(i)))})}function it(e){return P((t,n)=>{let r=null,o=!1,i;r=t.subscribe(O(n,void 0,void 0,s=>{i=U(e(s,it(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Ml(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(O(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function st(e,t){return b(t)?q(e,t,1):q(e,1)}function at(e){return P((t,n)=>{let r=!1;t.subscribe(O(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function ze(e){return e<=0?()=>se:P((t,n)=>{let r=0;t.subscribe(O(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Ao(e=Qg){return P((t,n)=>{let r=!1;t.subscribe(O(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Qg(){return new He}function bt(e){return P((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ge(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ce((o,i)=>e(o,i,r)):fe,ze(1),n?at(t):Ao(()=>new He))}function dn(e){return e<=0?()=>se:P((t,n)=>{let r=[];t.subscribe(O(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ws(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ce((o,i)=>e(o,i,r)):fe,dn(1),n?at(t):Ao(()=>new He))}function qs(e,t){return P(Ml(e,t,arguments.length>=2,!0))}function Zs(...e){let t=ot(e);return P((n,r)=>{(t?ln(e,n,t):ln(e,n)).subscribe(r)})}function ue(e,t){return P((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(O(r,c=>{o?.unsubscribe();let u=0,l=i++;U(e(c,l)).subscribe(o=O(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function xo(e){return P((t,n)=>{U(e).subscribe(O(n,()=>n.complete(),zn)),!n.closed&&t.subscribe(n)})}function Q(e,t,n){let r=b(e)||t||n?{next:e,error:t,complete:n}:e;return r?P((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(O(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):fe}var jo="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(Bo(t,n)),this.code=t}};function Jg(e){return`NG0${Math.abs(e)}`}function Bo(e,t){return`${Jg(e)}${t?": "+t:""}`}function B(e){for(let t in e)if(e[t]===B)return t;throw Error("")}function pe(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(pe).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function aa(e,t){return e?t?`${e} ${t}`:e:t||""}var Xg=B({__forward_ref__:B});function Vo(e){return e.__forward_ref__=Vo,e.toString=function(){return pe(this())},e}function le(e){return ca(e)?e():e}function ca(e){return typeof e=="function"&&e.hasOwnProperty(Xg)&&e.__forward_ref__===Vo}function xl(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function I(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Nt(e){return{providers:e.providers||[],imports:e.imports||[]}}function Yn(e){return em(e,Uo)}function ua(e){return Yn(e)!==null}function em(e,t){return e.hasOwnProperty(t)&&e[t]||null}function tm(e){let t=e?.[Uo]??null;return t||null}function Qs(e){return e&&e.hasOwnProperty(Po)?e[Po]:null}var Uo=B({\u0275prov:B}),Po=B({\u0275inj:B}),D=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=I({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function la(e){return e&&!!e.\u0275providers}var da=B({\u0275cmp:B}),fa=B({\u0275dir:B}),ha=B({\u0275pipe:B}),pa=B({\u0275mod:B}),qn=B({\u0275fac:B}),Rt=B({__NG_ELEMENT_ID__:B}),Nl=B({__NG_ENV_ID__:B});function hn(e){return typeof e=="string"?e:e==null?"":String(e)}function Ol(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():hn(e)}function ga(e,t){throw new v(-200,e)}function $o(e,t){throw new v(-201,!1)}var Ks;function Pl(){return Ks}function he(e){let t=Ks;return Ks=e,t}function ma(e,t,n){let r=Yn(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;$o(e,"Injector")}var nm={},Tt=nm,Js="__NG_DI_FLAG__",Xs=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=_t(n)||0;try{return this.injector.get(t,r&8?null:Tt,r)}catch(o){if(en(o))return o;throw o}}},ko="ngTempTokenPath",rm="ngTokenPath",om=/\n/gm,im="\u0275",Rl="__source";function sm(e,t=0){let n=Ts();if(n===void 0)throw new v(-203,!1);if(n===null)return ma(e,void 0,t);{let r=am(t),o=n.retrieve(e,r);if(en(o)){if(r.optional)return null;throw o}return o}}function w(e,t=0){return(Pl()||sm)(le(e),t)}function p(e,t){return w(e,_t(t))}function _t(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function am(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function ea(e){let t=[];for(let n=0;n<e.length;n++){let r=le(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=cm(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(w(o,i))}else t.push(w(r))}return t}function ya(e,t){return e[Js]=t,e.prototype[Js]=t,e}function cm(e){return e[Js]}function um(e,t,n,r){let o=e[ko];throw t[Rl]&&o.unshift(t[Rl]),e.message=lm(`
`+e.message,o,n,r),e[rm]=o,e[ko]=null,e}function lm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==im?e.slice(2):e;let o=pe(t);if(Array.isArray(t))o=t.map(pe).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):pe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(om,`
  `)}`}function St(e,t){let n=e.hasOwnProperty(qn);return n?e[qn]:null}function Ho(e,t){e.forEach(n=>Array.isArray(n)?Ho(n,t):t(n))}function va(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Qn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}var At={},_e=[],qe=new D(""),Da=new D("",-1),Ea=new D(""),Zn=class{get(t,n=Tt){if(n===Tt)throw new eo(`NullInjectorError: No provider for ${pe(t)}!`);return n}};function Ia(e){return e[pa]||null}function lt(e){return e[da]||null}function wa(e){return e[fa]||null}function kl(e){return e[ha]||null}function Ze(e){return{\u0275providers:e}}function Fl(e){return Ze([{provide:qe,multi:!0,useValue:e}])}function Ll(...e){return{\u0275providers:Ca(!0,e),\u0275fromNgModule:!0}}function Ca(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Ho(t,s=>{let a=s;Fo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&jl(o,i),n}function jl(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ba(o,i=>{t(i,r)})}}function Fo(e,t,n,r){if(e=le(e),!e)return!1;let o=null,i=Qs(e),s=!i&&lt(e);if(!i&&!s){let c=e.ngModule;if(i=Qs(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Fo(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Ho(i.imports,l=>{Fo(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&jl(u,t)}if(!a){let u=St(o)||(()=>new o);t({provide:o,useFactory:u,deps:_e},o),t({provide:Ea,useValue:o,multi:!0},o),t({provide:qe,useValue:()=>w(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;ba(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function ba(e,t){for(let n of e)la(n)&&(n=n.\u0275providers),Array.isArray(n)?ba(n,t):t(n)}var dm=B({provide:String,useValue:B});function Bl(e){return e!==null&&typeof e=="object"&&dm in e}function fm(e){return!!(e&&e.useExisting)}function hm(e){return!!(e&&e.useFactory)}function Lo(e){return typeof e=="function"}var Kn=new D(""),Oo={},Al={},Ys;function Jn(){return Ys===void 0&&(Ys=new Zn),Ys}var Z=class{},Mt=class extends Z{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,na(t,s=>this.processProvider(s)),this.records.set(Da,fn(void 0,this)),o.has("environment")&&this.records.set(Z,fn(void 0,this));let i=this.records.get(Kn);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ea,_e,{self:!0}))}retrieve(t,n){let r=_t(n)||0;try{return this.get(t,Tt,r)}catch(o){if(en(o))return o;throw o}}destroy(){Wn(this),this._destroyed=!0;let t=R(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),R(t)}}onDestroy(t){return Wn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Wn(this);let n=$e(this),r=he(void 0),o;try{return t()}finally{$e(n),he(r)}}get(t,n=Tt,r){if(Wn(this),t.hasOwnProperty(Nl))return t[Nl](this);let o=_t(r),i,s=$e(this),a=he(void 0);try{if(!(o&4)){let u=this.records.get(t);if(u===void 0){let l=vm(t)&&Yn(t);l&&this.injectableDefInScope(l)?u=fn(ta(t),Oo):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let c=o&2?Jn():this.parent;return n=o&8&&n===Tt?null:n,c.get(t,n)}catch(c){if(en(c)){if((c[ko]=c[ko]||[]).unshift(pe(t)),s)throw c;return um(c,t,"R3InjectorError",this.source)}else throw c}finally{he(a),$e(s)}}resolveInjectorInitializers(){let t=R(null),n=$e(this),r=he(void 0),o;try{let i=this.get(qe,_e,{self:!0});for(let s of i)s()}finally{$e(n),he(r),R(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(pe(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=le(t);let n=Lo(t)?t:le(t&&t.provide),r=gm(t);if(!Lo(t)&&t.multi===!0){let o=this.records.get(n);o||(o=fn(void 0,Oo,!0),o.factory=()=>ea(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=R(null);try{return n.value===Al?ga(pe(t)):n.value===Oo&&(n.value=Al,n.value=n.factory()),typeof n.value=="object"&&n.value&&ym(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{R(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=le(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function ta(e){let t=Yn(e),n=t!==null?t.factory:St(e);if(n!==null)return n;if(e instanceof D)throw new v(204,!1);if(e instanceof Function)return pm(e);throw new v(204,!1)}function pm(e){if(e.length>0)throw new v(204,!1);let n=tm(e);return n!==null?()=>n.factory(e):()=>new e}function gm(e){if(Bl(e))return fn(void 0,e.useValue);{let t=Vl(e);return fn(t,Oo)}}function Vl(e,t,n){let r;if(Lo(e)){let o=le(e);return St(o)||ta(o)}else if(Bl(e))r=()=>le(e.useValue);else if(hm(e))r=()=>e.useFactory(...ea(e.deps||[]));else if(fm(e))r=()=>w(le(e.useExisting));else{let o=le(e&&(e.useClass||e.provide));if(mm(e))r=()=>new o(...ea(e.deps));else return St(o)||ta(o)}return r}function Wn(e){if(e.destroyed)throw new v(205,!1)}function fn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function mm(e){return!!e.deps}function ym(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function vm(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function na(e,t){for(let n of e)Array.isArray(n)?na(n,t):n&&la(n)?na(n.\u0275providers,t):t(n)}function re(e,t){let n;e instanceof Mt?(Wn(e),n=e):n=new Xs(e);let r,o=$e(n),i=he(void 0);try{return t()}finally{$e(o),he(i)}}function Ul(){return Pl()!==void 0||Ts()!=null}var Se=0,N=1,T=2,K=3,De=4,Ee=5,pn=6,gn=7,Y=8,xt=9,Pe=10,J=11,mn=12,Ta=13,Ot=14,ge=15,Pt=16,kt=17,Ft=18,Xn=19,_a=20,We=21,zo=22,er=23,me=24,Go=25,Ie=26,$l=1,Sa=6,dt=7,tr=8,nr=9,ne=10;function ke(e){return Array.isArray(e)&&typeof e[$l]=="object"}function Me(e){return Array.isArray(e)&&e[$l]===!0}function Ma(e){return(e.flags&4)!==0}function ft(e){return e.componentOffset>-1}function Wo(e){return(e.flags&1)===1}function Lt(e){return!!e.template}function yn(e){return(e[T]&512)!==0}function jt(e){return(e[T]&256)===256}var Na="svg",Hl="math";function we(e){for(;Array.isArray(e);)e=e[Se];return e}function zl(e,t){return we(t[e])}function Fe(e,t){return we(t[e.index])}function rr(e,t){return e.data[t]}function Ce(e,t){let n=t[e];return ke(n)?n:n[Se]}function qo(e){return(e[T]&128)===128}function Gl(e){return Me(e[K])}function Bt(e,t){return t==null?null:e[t]}function Ra(e){e[kt]=0}function Aa(e){e[T]&1024||(e[T]|=1024,qo(e)&&ir(e))}function Wl(e,t){for(;e>0;)t=t[Ot],e--;return t}function or(e){return!!(e[T]&9216||e[me]?.dirty)}function Zo(e){e[Pe].changeDetectionScheduler?.notify(8),e[T]&64&&(e[T]|=1024),or(e)&&ir(e)}function ir(e){e[Pe].changeDetectionScheduler?.notify(0);let t=ct(e);for(;t!==null&&!(t[T]&8192||(t[T]|=8192,!qo(t)));)t=ct(t)}function xa(e,t){if(jt(e))throw new v(911,!1);e[We]===null&&(e[We]=[]),e[We].push(t)}function ql(e,t){if(e[We]===null)return;let n=e[We].indexOf(t);n!==-1&&e[We].splice(n,1)}function ct(e){let t=e[K];return Me(t)?t[K]:t}function Zl(e){return e[gn]??=[]}function Yl(e){return e.cleanup??=[]}var x={lFrame:dd(null),bindingsEnabled:!0,skipHydrationRootTNode:null},sr=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(sr||{}),Dm=0,ra=!1;function Ql(){return x.lFrame.elementDepthCount}function Kl(){x.lFrame.elementDepthCount++}function Jl(){x.lFrame.elementDepthCount--}function Oa(){return x.bindingsEnabled}function Xl(){return x.skipHydrationRootTNode!==null}function ed(e){return x.skipHydrationRootTNode===e}function td(){x.skipHydrationRootTNode=null}function L(){return x.lFrame.lView}function Ye(){return x.lFrame.tView}function be(){let e=Pa();for(;e!==null&&e.type===64;)e=e.parent;return e}function Pa(){return x.lFrame.currentTNode}function nd(){let e=x.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function vn(e,t){let n=x.lFrame;n.currentTNode=e,n.isParent=t}function ka(){return x.lFrame.isParent}function rd(){x.lFrame.isParent=!1}function Fa(e){xl("Must never be called in production mode"),Dm=e}function La(){return ra}function ja(e){let t=ra;return ra=e,t}function Ba(){let e=x.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function od(){return x.lFrame.bindingIndex}function id(e){return x.lFrame.bindingIndex=e}function Yo(){return x.lFrame.bindingIndex++}function sd(e){let t=x.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function ad(){return x.lFrame.inI18n}function cd(e,t){let n=x.lFrame;n.bindingIndex=n.bindingRootIndex=e,Qo(t)}function ud(){return x.lFrame.currentDirectiveIndex}function Qo(e){x.lFrame.currentDirectiveIndex=e}function Va(e){x.lFrame.currentQueryIndex=e}function Em(e){let t=e[N];return t.type===2?t.declTNode:t.type===1?e[Ee]:null}function Ua(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=Em(i),o===null||(i=i[Ot],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=x.lFrame=ld();return r.currentTNode=t,r.lView=e,!0}function Ko(e){let t=ld(),n=e[N];x.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ld(){let e=x.lFrame,t=e===null?null:e.child;return t===null?dd(e):t}function dd(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function fd(){let e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var $a=fd;function Jo(){let e=fd();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function hd(e){return(x.lFrame.contextLView=Wl(e,x.lFrame.contextLView))[Y]}function Dn(){return x.lFrame.selectedIndex}function ht(e){x.lFrame.selectedIndex=e}function pd(){let e=x.lFrame;return rr(e.tView,e.selectedIndex)}function gd(){x.lFrame.currentNamespace=Na}function md(){Im()}function Im(){x.lFrame.currentNamespace=null}function yd(){return x.lFrame.currentNamespace}var vd=!0;function Xo(){return vd}function ei(e){vd=e}function oa(e,t=null,n=null,r){let o=Ha(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ha(e,t=null,n=null,r,o=new Set){let i=[n||_e,Ll(e)];return r=r||(typeof e=="object"?void 0:pe(e)),new Mt(i,t||Jn(),r||null,o)}var xe=class e{static THROW_IF_NOT_FOUND=Tt;static NULL=new Zn;static create(t,n){if(Array.isArray(t))return oa({name:""},n,t,"");{let r=t.name??"";return oa({name:r},t.parent,t.providers,r)}}static \u0275prov=I({token:e,providedIn:"any",factory:()=>w(Da)});static __NG_ELEMENT_ID__=-1},H=new D(""),pt=(()=>{class e{static __NG_ELEMENT_ID__=wm;static __NG_ENV_ID__=n=>n}return e})(),ia=class extends pt{_lView;constructor(t){super(),this._lView=t}get destroyed(){return jt(this._lView)}onDestroy(t){let n=this._lView;return xa(n,t),()=>ql(n,t)}};function wm(){return new ia(L())}var Oe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},de=new D("",{providedIn:"root",factory:()=>{let e=p(Z),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(Oe),t.handleError(n))}}}),Dd={provide:qe,useValue:()=>void p(Oe),multi:!0},Cm=new D("",{providedIn:"root",factory:()=>{let e=p(H).defaultView;if(!e)return;let t=p(de),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),p(pt).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function bm(){return Ze([Fl(()=>void p(Cm))])}var ut=class{},ar=new D("",{providedIn:"root",factory:()=>!1});var za=new D(""),Ga=new D("");var Qe=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new te(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new A(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})(),ti=(()=>{class e{internalPendingTasks=p(Qe);scheduler=p(ut);errorHandler=p(de);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();function cr(...e){}var Wa=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>new sa})}return e})(),sa=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function Cn(e){return{toString:e}.toString()}var ni="__parameters__";function km(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Jd(e,t,n){return Cn(()=>{let r=km(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(ni)?c[ni]:Object.defineProperty(c,ni,{value:[]})[ni];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Xd=ya(Jd("Optional"),8);var ef=ya(Jd("SkipSelf"),4);function Fm(e){return typeof e=="function"}var si=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function tf(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Ii=(()=>{let e=()=>nf;return e.ngInherit=!0,e})();function nf(e){return e.type.prototype.ngOnChanges&&(e.setInput=jm),Lm}function Lm(){let e=of(this),t=e?.current;if(t){let n=e.previous;if(n===At)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function jm(e,t,n,r,o){let i=this.declaredInputs[r],s=of(e)||Bm(e,{previous:At,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new si(u&&u.currentValue,n,c===At),tf(e,t,o,n)}var rf="__ngSimpleChanges__";function of(e){return e[rf]||null}function Bm(e,t){return e[rf]=t}var Ed=[];var F=function(e,t=null,n){for(let r=0;r<Ed.length;r++){let o=Ed[r];o(e,t,n)}};function Vm(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=nf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function sf(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function ri(e,t,n){af(e,t,3,n)}function oi(e,t,n,r){(e[T]&3)===n&&af(e,t,n,r)}function qa(e,t){let n=e[T];(n&3)===t&&(n&=16383,n+=1,e[T]=n)}function af(e,t,n,r){let o=r!==void 0?e[kt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[kt]+=65536),(a<i||i==-1)&&(Um(e,n,t,c),e[kt]=(e[kt]&**********)+c+2),c++}function Id(e,t){F(4,e,t);let n=R(null);try{t.call(e)}finally{R(n),F(5,e,t)}}function Um(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[T]>>14<e[kt]>>16&&(e[T]&3)===t&&(e[T]+=16384,Id(a,i)):Id(a,i)}var In=-1,dr=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function $m(e){return(e.flags&8)!==0}function Hm(e){return(e.flags&16)!==0}function zm(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Wm(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Gm(e){return e===3||e===4||e===6}function Wm(e){return e.charCodeAt(0)===64}function Ic(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?wd(e,n,o,null,t[++r]):wd(e,n,o,null,null))}}return e}function wd(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function cf(e){return e!==In}function ai(e){return e&32767}function qm(e){return e>>16}function ci(e,t){let n=qm(e),r=t;for(;n>0;)r=r[Ot],n--;return r}var Xa=!0;function Cd(e){let t=Xa;return Xa=e,t}var Zm=256,uf=Zm-1,lf=5,Ym=0,Le={};function Qm(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Rt)&&(r=n[Rt]),r==null&&(r=n[Rt]=Ym++);let o=r&uf,i=1<<o;t.data[e+(o>>lf)]|=i}function df(e,t){let n=ff(e,t);if(n!==-1)return n;let r=t[N];r.firstCreatePass&&(e.injectorIndex=t.length,Za(r.data,e),Za(t,null),Za(r.blueprint,null));let o=wc(e,t),i=e.injectorIndex;if(cf(o)){let s=ai(o),a=ci(o,t),c=a[N].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Za(e,t){e.push(0,0,0,0,0,0,0,0,t)}function ff(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function wc(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=yf(o),r===null)return In;if(n++,o=o[Ot],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return In}function Km(e,t,n){Qm(e,t,n)}function hf(e,t,n){if(n&8||e!==void 0)return e;$o(t,"NodeInjector")}function pf(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[xt],i=he(void 0);try{return o?o.get(t,r,n&8):ma(t,r,n&8)}finally{he(i)}}return hf(r,t,n)}function gf(e,t,n,r=0,o){if(e!==null){if(t[T]&2048&&!(r&2)){let s=ny(e,t,n,r,Le);if(s!==Le)return s}let i=mf(e,t,n,r,Le);if(i!==Le)return i}return pf(t,n,r,o)}function mf(e,t,n,r,o){let i=ey(n);if(typeof i=="function"){if(!Ua(t,e,r))return r&1?hf(o,n,r):pf(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))$o(n);else return s}finally{$a()}}else if(typeof i=="number"){let s=null,a=ff(e,t),c=In,u=r&1?t[ge][Ee]:null;for((a===-1||r&4)&&(c=a===-1?wc(e,t):t[a+8],c===In||!Td(r,!1)?a=-1:(s=t[N],a=ai(c),t=ci(c,t)));a!==-1;){let l=t[N];if(bd(i,a,l.data)){let d=Jm(a,t,n,s,r,u);if(d!==Le)return d}c=t[a+8],c!==In&&Td(r,t[N].data[a+8]===u)&&bd(i,a,t)?(s=l,a=ai(c),t=ci(c,t)):a=-1}}return o}function Jm(e,t,n,r,o,i){let s=t[N],a=s.data[e+8],c=r==null?ft(a)&&Xa:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Xm(a,s,n,c,u);return l!==null?ec(t,s,l,a):Le}function Xm(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&Lt(f)&&f.type===n)return c}return null}function ec(e,t,n,r){let o=e[n],i=t.data;if(o instanceof dr){let s=o;s.resolving&&ga(Ol(i[n]));let a=Cd(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],u,l=s.injectImpl?he(s.injectImpl):null,d=Ua(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Vm(n,i[n],t)}finally{l!==null&&he(l),Cd(a),s.resolving=!1,$a()}}return o}function ey(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Rt)?e[Rt]:void 0;return typeof t=="number"?t>=0?t&uf:ty:t}function bd(e,t,n){let r=1<<e;return!!(n[t+(e>>lf)]&r)}function Td(e,t){return!(e&2)&&!(e&1&&t)}var Vt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return gf(this._tNode,this._lView,t,_t(r),n)}};function ty(){return new Vt(be(),L())}function wi(e){return Cn(()=>{let t=e.prototype.constructor,n=t[qn]||tc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[qn]||tc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function tc(e){return ca(e)?()=>{let t=tc(le(e));return t&&t()}:St(e)}function ny(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[T]&2048&&!yn(s);){let a=mf(i,s,n,r|2,Le);if(a!==Le)return a;let c=i.parent;if(!c){let u=s[_a];if(u){let l=u.get(n,Le,r);if(l!==Le)return l}c=yf(s),s=s[Ot]}i=c}return o}function yf(e){let t=e[N],n=t.type;return n===2?t.declTNode:n===1?e[Ee]:null}function ry(){return Ci(be(),L())}function Ci(e,t){return new bi(Fe(e,t))}var bi=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=ry}return e})();function vf(e){return(e.flags&128)===128}var Cc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Cc||{}),Df=new Map,oy=0;function iy(){return oy++}function sy(e){Df.set(e[Xn],e)}function nc(e){Df.delete(e[Xn])}var _d="__ngContext__";function mr(e,t){ke(t)?(e[_d]=t[Xn],sy(t)):e[_d]=t}function Ef(e){return wf(e[mn])}function If(e){return wf(e[De])}function wf(e){for(;e!==null&&!Me(e);)e=e[De];return e}var rc;function bc(e){rc=e}function Cf(){if(rc!==void 0)return rc;if(typeof document<"u")return document;throw new v(210,!1)}var Ti=new D("",{providedIn:"root",factory:()=>ay}),ay="ng",_i=new D(""),bn=new D("",{providedIn:"platform",factory:()=>"unknown"});var Si=new D("",{providedIn:"root",factory:()=>Cf().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var cy="h",uy="b";var bf="r";var Tf="di";var _f=!1,Sf=new D("",{providedIn:"root",factory:()=>_f});var ly=(e,t,n,r)=>{};function dy(e,t,n,r){ly(e,t,n,r)}var fy=()=>null;function Mf(e,t,n=!1){return fy(e,t,n)}function Nf(e,t){let n=e.contentQueries;if(n!==null){let r=R(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Va(i),a.contentQueries(2,t[s],s)}}}finally{R(r)}}}function oc(e,t,n){Va(0);let r=R(null);try{t(e,n)}finally{R(r)}}function Rf(e,t,n){if(Ma(t)){let r=R(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{R(r)}}}var Ke=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ke||{});var ui=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${jo})`}};function Af(e){return e instanceof ui?e.changingThisBreaksApplicationSecurity:e}function xf(e,t){let n=Of(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${jo})`)}return n===t}function Of(e){return e instanceof ui&&e.getTypeName()||null}var hy=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Pf(e){return e=String(e),e.match(hy)?e:"unsafe:"+e}var Tc=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Tc||{});function py(e){let t=gy();return t?t.sanitize(Tc.URL,e)||"":xf(e,"URL")?Af(e):Pf(hn(e))}function gy(){let e=L();return e&&e[Pe].sanitizer}function kf(e){return e instanceof Function?e():e}function my(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Ff="ng-template";function yy(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&my(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(_c(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function _c(e){return e.type===4&&e.value!==Ff}function vy(e,t,n){let r=e.type===4&&!n?Ff:e.value;return t===r}function Dy(e,t,n){let r=4,o=e.attrs,i=o!==null?wy(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ne(r)&&!Ne(c))return!1;if(s&&Ne(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!vy(e,c,n)||c===""&&t.length===1){if(Ne(r))return!1;s=!0}}else if(r&8){if(o===null||!yy(e,o,c,n)){if(Ne(r))return!1;s=!0}}else{let u=t[++a],l=Ey(c,o,_c(e),n);if(l===-1){if(Ne(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Ne(r))return!1;s=!0}}}}return Ne(r)||s}function Ne(e){return(e&1)===0}function Ey(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Cy(t,e)}function Iy(e,t,n=!1){for(let r=0;r<t.length;r++)if(Dy(e,t[r],n))return!0;return!1}function wy(e){for(let t=0;t<e.length;t++){let n=e[t];if(Gm(n))return t}return e.length}function Cy(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Sd(e,t){return e?":not("+t.trim()+")":t}function by(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ne(s)&&(t+=Sd(i,o),o=""),r=s,i=i||!Ne(r);n++}return o!==""&&(t+=Sd(i,o)),t}function Ty(e){return e.map(by).join(",")}function _y(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ne(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Je={};function Sy(e,t){return e.createText(t)}function My(e,t,n){e.setValue(t,n)}function Lf(e,t,n){return e.createElement(t,n)}function li(e,t,n,r,o){e.insertBefore(t,n,r,o)}function jf(e,t,n){e.appendChild(t,n)}function Md(e,t,n,r,o){r!==null?li(e,t,n,r,o):jf(e,t,n)}function Bf(e,t,n){e.removeChild(null,t,n)}function Ny(e,t,n){e.setAttribute(t,"style",n)}function Ry(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Vf(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&zm(e,t,r),o!==null&&Ry(e,t,o),i!==null&&Ny(e,t,i)}function Sc(e,t,n,r,o,i,s,a,c,u,l){let d=Ie+r,h=d+o,f=Ay(d,h),g=typeof u=="function"?u():u;return f[N]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function Ay(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Je);return n}function xy(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Sc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Mc(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[Se]=o,d[T]=r|4|128|8|64|1024,(u!==null||e&&e[T]&2048)&&(d[T]|=2048),Ra(d),d[K]=d[Ot]=e,d[Y]=n,d[Pe]=s||e&&e[Pe],d[J]=a||e&&e[J],d[xt]=c||e&&e[xt]||null,d[Ee]=i,d[Xn]=iy(),d[pn]=l,d[_a]=u,d[ge]=t.type==2?e[ge]:d,d}function Oy(e,t,n){let r=Fe(t,e),o=xy(n),i=e[Pe].rendererFactory,s=Nc(e,Mc(e,o,null,Uf(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Uf(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function $f(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Nc(e,t){return e[mn]?e[Ta][De]=t:e[mn]=t,e[Ta]=t,t}function Py(e=1){Hf(Ye(),L(),Dn()+e,!1)}function Hf(e,t,n,r){if(!r)if((t[T]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ri(t,i,n)}else{let i=e.preOrderHooks;i!==null&&oi(t,i,0,n)}ht(n)}var Mi=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Mi||{});function ic(e,t,n,r){let o=R(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Mi.SignalBased)!==0&&(c=t[i][no]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):tf(t,c,i,r)}finally{R(o)}}function zf(e,t,n,r,o){let i=Dn(),s=r&2;try{ht(-1),s&&t.length>Ie&&Hf(e,t,Ie,!1),F(s?2:0,o,n),n(r,o)}finally{ht(i),F(s?3:1,o,n)}}function Rc(e,t,n){$y(e,t,n),(n.flags&64)===64&&Hy(e,t,n)}function Gf(e,t,n=Fe){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function ky(e,t,n,r){let i=r.get(Sf,_f)||n===Ke.ShadowDom,s=e.selectRootElement(t,i);return Fy(s),s}function Fy(e){Ly(e)}var Ly=()=>null;function jy(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function By(e,t,n,r,o,i){let s=t[N];if(Ac(e,s,t,n,r)){ft(e)&&Uy(t,e.index);return}Vy(e,t,n,r,o,i)}function Vy(e,t,n,r,o,i){if(e.type&3){let s=Fe(e,t);n=jy(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function Uy(e,t){let n=Ce(t,e);n[T]&16||(n[T]|=64)}function $y(e,t,n){let r=n.directiveStart,o=n.directiveEnd;ft(n)&&Oy(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||df(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=ec(t,e,s,n);if(mr(c,t),i!==null&&Gy(t,s-r,c,a,n,i),Lt(a)){let u=Ce(n.index,t);u[Y]=ec(t,e,s,n)}}}function Hy(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=ud();try{ht(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];Qo(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&zy(c,u)}}finally{ht(-1),Qo(s)}}function zy(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Wf(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Iy(t,i.selectors,!1)&&(r??=[],Lt(i)?r.unshift(i):r.push(i))}return r}function Gy(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];ic(r,n,c,u)}}function Wy(e,t){let n=e[xt];if(!n)return;n.get(de,null)?.(t)}function Ac(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];ic(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];ic(l,u,r,o),a=!0}return a}function qy(e,t){let n=Ce(t,e),r=n[N];Zy(r,n);let o=n[Se];o!==null&&n[pn]===null&&(n[pn]=Mf(o,n[xt])),F(18),xc(r,n,n[Y]),F(19,n[Y])}function Zy(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function xc(e,t,n){Ko(t);try{let r=e.viewQuery;r!==null&&oc(1,r,n);let o=e.template;o!==null&&zf(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ft]?.finishViewCreation(e),e.staticContentQueries&&Nf(e,t),e.staticViewQueries&&oc(2,e.viewQuery,n);let i=e.components;i!==null&&Yy(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[T]&=-5,Jo()}}function Yy(e,t){for(let n=0;n<t.length;n++)qy(e,t[n])}function Oc(e,t,n,r){let o=R(null);try{let i=t.tView,a=e[T]&4096?4096:16,c=Mc(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[Pt]=u;let l=e[Ft];return l!==null&&(c[Ft]=l.createEmbeddedView(i)),xc(i,c,n),c}finally{R(o)}}function di(e,t){return!t||t.firstChild===null||vf(e)}var Nd=!1,Qy=new D(""),Ky;function Pc(e,t){return Ky(e,t)}var mt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(mt||{});function kc(e){return(e.flags&32)===32}function En(e,t,n,r,o){if(r!=null){let i,s=!1;Me(r)?i=r:ke(r)&&(s=!0,r=r[Se]);let a=we(r);e===0&&n!==null?o==null?jf(t,n,a):li(t,n,a,o||null,!0):e===1&&n!==null?li(t,n,a,o||null,!0):e===2?Bf(t,a,s):e===3&&t.destroyNode(a),i!=null&&uv(t,e,i,n,o)}}function Jy(e,t){qf(e,t),t[Se]=null,t[Ee]=null}function Xy(e,t,n,r,o,i){r[Se]=o,r[Ee]=t,Ri(e,r,n,1,o,i)}function qf(e,t){t[Pe].changeDetectionScheduler?.notify(9),Ri(e,t,t[J],2,null,null)}function ev(e){let t=e[mn];if(!t)return Ya(e[N],e);for(;t;){let n=null;if(ke(t))n=t[mn];else{let r=t[ne];r&&(n=r)}if(!n){for(;t&&!t[De]&&t!==e;)ke(t)&&Ya(t[N],t),t=t[K];t===null&&(t=e),ke(t)&&Ya(t[N],t),n=t&&t[De]}t=n}}function Fc(e,t){let n=e[nr],r=n.indexOf(t);n.splice(r,1)}function Ni(e,t){if(jt(t))return;let n=t[J];n.destroyNode&&Ri(e,t,n,3,null,null),ev(t)}function Ya(e,t){if(jt(t))return;let n=R(null);try{t[T]&=-129,t[T]|=256,t[me]&&xs(t[me]),nv(e,t),tv(e,t),t[N].type===1&&t[J].destroy();let r=t[Pt];if(r!==null&&Me(t[K])){r!==t[K]&&Fc(r,t);let o=t[Ft];o!==null&&o.detachView(e)}nc(t)}finally{R(n)}}function tv(e,t){let n=e.cleanup,r=t[gn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[gn]=null);let o=t[We];if(o!==null){t[We]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[er];if(i!==null){t[er]=null;for(let s of i)s.destroy()}}function nv(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof dr)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];F(4,a,c);try{c.call(a)}finally{F(5,a,c)}}else{F(4,o,i);try{i.call(o)}finally{F(5,o,i)}}}}}function rv(e,t,n){return ov(e,t.parent,n)}function ov(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Se];if(ft(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ke.None||o===Ke.Emulated)return null}return Fe(r,n)}function iv(e,t,n){return av(e,t,n)}function sv(e,t,n){return e.type&40?Fe(e,n):null}var av=sv,Rd;function Lc(e,t,n,r){let o=rv(e,r,t),i=t[J],s=r.parent||t[Ee],a=iv(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Md(i,o,n[c],a,!1);else Md(i,o,n,a,!1);Rd!==void 0&&Rd(i,r,t,n,o)}function ur(e,t){if(t!==null){let n=t.type;if(n&3)return Fe(t,e);if(n&4)return sc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return ur(e,r);{let o=e[t.index];return Me(o)?sc(-1,o):we(o)}}else{if(n&128)return ur(e,t.next);if(n&32)return Pc(t,e)()||we(e[t.index]);{let r=Zf(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=ct(e[ge]);return ur(o,r)}else return ur(e,t.next)}}}return null}function Zf(e,t){if(t!==null){let r=e[ge][Ee],o=t.projection;return r.projection[o]}return null}function sc(e,t){let n=ne+e+1;if(n<t.length){let r=t[n],o=r[N].firstChild;if(o!==null)return ur(r,o)}return t[dt]}function jc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&mr(we(a),r),n.flags|=2),!kc(n))if(c&8)jc(e,t,n.child,r,o,i,!1),En(t,e,o,a,i);else if(c&32){let u=Pc(n,r),l;for(;l=u();)En(t,e,o,l,i);En(t,e,o,a,i)}else c&16?cv(e,t,r,n,o,i):En(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Ri(e,t,n,r,o,i){jc(n,r,e.firstChild,t,o,i,!1)}function cv(e,t,n,r,o,i){let s=n[ge],c=s[Ee].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];En(t,e,o,l,i)}else{let u=c,l=s[K];vf(r)&&(u.flags|=128),jc(e,t,u,l,o,i,!0)}}function uv(e,t,n,r,o){let i=n[dt],s=we(n);i!==s&&En(t,e,r,i,o);for(let a=ne;a<n.length;a++){let c=n[a];Ri(c[N],c,e,t,r,i)}}function fr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(we(i)),Me(i)&&Yf(i,r);let s=n.type;if(s&8)fr(e,t,n.child,r);else if(s&32){let a=Pc(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Zf(t,n);if(Array.isArray(a))r.push(...a);else{let c=ct(t[ge]);fr(c[N],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Yf(e,t){for(let n=ne;n<e.length;n++){let r=e[n],o=r[N].firstChild;o!==null&&fr(r[N],r,o,t)}e[dt]!==e[Se]&&t.push(e[dt])}function Qf(e){if(e[Go]!==null){for(let t of e[Go])t.impl.addSequence(t);e[Go].length=0}}var Kf=[];function lv(e){return e[me]??dv(e)}function dv(e){let t=Kf.pop()??Object.create(hv);return t.lView=e,t}function fv(e){e.lView[me]!==e&&(e.lView=null,Kf.push(e))}var hv=j(y({},ro),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ir(e.lView)},consumerOnSignalRead(){this.lView[me]=this}});function pv(e){let t=e[me]??Object.create(gv);return t.lView=e,t}var gv=j(y({},ro),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=ct(e.lView);for(;t&&!Jf(t[N]);)t=ct(t);t&&Aa(t)},consumerOnSignalRead(){this.lView[me]=this}});function Jf(e){return e.type!==2}function Xf(e){if(e[er]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[er])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[T]&8192)}}var mv=100;function Bc(e,t=0){let r=e[Pe].rendererFactory,o=!1;o||r.begin?.();try{yv(e,t)}finally{o||r.end?.()}}function yv(e,t){let n=La();try{ja(!0),ac(e,t);let r=0;for(;or(e);){if(r===mv)throw new v(103,!1);r++,ac(e,1)}}finally{ja(n)}}function eh(e,t){Fa(t?sr.Exhaustive:sr.OnlyDirtyViews);try{Bc(e)}finally{Fa(sr.Off)}}function vv(e,t,n,r){if(jt(t))return;let o=t[T],i=!1,s=!1;Ko(t);let a=!0,c=null,u=null;i||(Jf(e)?(u=lv(t),c=oo(u)):Ms()===null?(a=!1,u=pv(t),c=oo(u)):t[me]&&(xs(t[me]),t[me]=null));try{Ra(t),id(e.bindingStartIndex),n!==null&&zf(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&ri(t,f,null)}else{let f=e.preOrderHooks;f!==null&&oi(t,f,0,null),qa(t,0)}if(s||Dv(t),Xf(t),th(t,0),e.contentQueries!==null&&Nf(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&ri(t,f)}else{let f=e.contentHooks;f!==null&&oi(t,f,1),qa(t,1)}Iv(e,t);let d=e.components;d!==null&&rh(t,d,0);let h=e.viewQuery;if(h!==null&&oc(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&ri(t,f)}else{let f=e.viewHooks;f!==null&&oi(t,f,2),qa(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[zo]){for(let f of t[zo])f();t[zo]=null}i||(Qf(t),t[T]&=-73)}catch(l){throw i||ir(t),l}finally{u!==null&&(As(u,c),a&&fv(u)),Jo()}}function th(e,t){for(let n=Ef(e);n!==null;n=If(n))for(let r=ne;r<n.length;r++){let o=n[r];nh(o,t)}}function Dv(e){for(let t=Ef(e);t!==null;t=If(t)){if(!(t[T]&2))continue;let n=t[nr];for(let r=0;r<n.length;r++){let o=n[r];Aa(o)}}}function Ev(e,t,n){F(18);let r=Ce(t,e);nh(r,n),F(19,r[Y])}function nh(e,t){qo(e)&&ac(e,t)}function ac(e,t){let r=e[N],o=e[T],i=e[me],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&io(i)),s||=!1,i&&(i.dirty=!1),e[T]&=-9217,s)vv(r,e,r.template,e[Y]);else if(o&8192){let a=R(null);try{Xf(e),th(e,1);let c=r.components;c!==null&&rh(e,c,1),Qf(e)}finally{R(a)}}}function rh(e,t,n){for(let r=0;r<t.length;r++)Ev(e,t[r],n)}function Iv(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)ht(~o);else{let i=o,s=n[++r],a=n[++r];cd(s,i);let c=t[i];F(24,c),a(2,c),F(25,c)}}}finally{ht(-1)}}function Vc(e,t){let n=La()?64:1088;for(e[Pe].changeDetectionScheduler?.notify(t);e;){e[T]|=n;let r=ct(e);if(yn(e)&&!r)return e;e=r}return null}function oh(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function wv(e,t){let n=ne+t;if(n<e.length)return e[n]}function Uc(e,t,n,r=!0){let o=t[N];if(bv(o,t,e,n),r){let s=sc(n,e),a=t[J],c=a.parentNode(e[dt]);c!==null&&Xy(o,e[Ee],a,t,c,s)}let i=t[pn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Cv(e,t){let n=hr(e,t);return n!==void 0&&Ni(n[N],n),n}function hr(e,t){if(e.length<=ne)return;let n=ne+t,r=e[n];if(r){let o=r[Pt];o!==null&&o!==e&&Fc(o,r),t>0&&(e[n-1][De]=r[De]);let i=Qn(e,ne+t);Jy(r[N],r);let s=i[Ft];s!==null&&s.detachView(i[N]),r[K]=null,r[De]=null,r[T]&=-129}return r}function bv(e,t,n,r){let o=ne+r,i=n.length;r>0&&(n[o-1][De]=t),r<i-ne?(t[De]=n[o],va(n,ne+r,t)):(n.push(t),t[De]=null),t[K]=n;let s=t[Pt];s!==null&&n!==s&&ih(s,t);let a=t[Ft];a!==null&&a.insertView(e),Zo(t),t[T]|=128}function ih(e,t){let n=e[nr],r=t[K];if(ke(r))e[T]|=2;else{let o=r[K][ge];t[ge]!==o&&(e[T]|=2)}n===null?e[nr]=[t]:n.push(t)}var gt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[N];return fr(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[Y]}set context(t){this._lView[Y]=t}get destroyed(){return jt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[K];if(Me(t)){let n=t[tr],r=n?n.indexOf(this):-1;r>-1&&(hr(t,r),Qn(n,r))}this._attachedToViewContainer=!1}Ni(this._lView[N],this._lView)}onDestroy(t){xa(this._lView,t)}markForCheck(){Vc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[T]&=-129}reattach(){Zo(this._lView),this._lView[T]|=128}detectChanges(){this._lView[T]|=1024,Bc(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[xt].get(Qy,Nd)}catch{this.exhaustive=Nd}}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=yn(this._lView),n=this._lView[Pt];n!==null&&!t&&Fc(n,this._lView),qf(this._lView[N],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=yn(this._lView),r=this._lView[Pt];r!==null&&!n&&ih(r,this._lView),Zo(this._lView)}};var yr=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Tv;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Oc(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new gt(i)}}return e})();function Tv(){return _v(be(),L())}function _v(e,t){return e.type&4?new yr(t,e,Ci(e,t)):null}function $c(e,t,n,r,o){let i=e.data[t];if(i===null)i=Sv(e,t,n,r,o),ad()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=nd();i.injectorIndex=s===null?-1:s.injectorIndex}return vn(i,!0),i}function Sv(e,t,n,r,o){let i=Pa(),s=ka(),a=s?i:i&&i.parent,c=e.data[t]=Nv(e,a,n,t,r,o);return Mv(e,c,i,s),c}function Mv(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Nv(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Xl()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var mR=new RegExp(`^(\\d+)*(${uy}|${cy})*(.*)`);function Rv(e){let t=e[Sa]??[],r=e[K][J],o=[];for(let i of t)i.data[Tf]!==void 0?o.push(i):Av(i,r);e[Sa]=o}function Av(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[bf];for(;n<o;){let i=r.nextSibling;Bf(t,r,!1),r=i,n++}}}var xv=()=>null,Ov=()=>null;function cc(e,t){return xv(e,t)}function Pv(e,t,n){return Ov(e,t,n)}var sh=class{},Ai=class{},uc=class{resolveComponentFactory(t){throw new v(917,!1)}},vr=class{static NULL=new uc},Ut=class{},Hc=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>kv()}return e})();function kv(){let e=L(),t=be(),n=Ce(t.index,e);return(ke(n)?n:e)[J]}var ah=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>null})}return e})();var ii={},lc=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,ii,r);return o!==ii||n===ii?o:this.parentInjector.get(t,n,r)}};function Ad(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=aa(o,a);else if(i==2){let c=a,u=t[++s];r=aa(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function je(e,t=0){let n=L();if(n===null)return w(e,t);let r=be();return gf(r,n,le(e),t)}function ch(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}jv(e,t,n,a,i,c,u)}i!==null&&r!==null&&Fv(n,r,i)}function Fv(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function Lv(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function jv(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&Lt(f)&&(c=!0,Lv(e,n,h)),Km(df(n,t),e,f.type)}zv(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=$f(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=Ic(n.mergedAttrs,f.hostAttrs),Vv(e,n,t,d,f),Hv(d,f,o),s!==null&&s.has(f)){let[m,E]=s.get(f);n.directiveToIndex.set(f.type,[d,m+n.directiveStart,E+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}Bv(e,n,i)}function Bv(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))xd(0,t,o,r),xd(1,t,o,r),Pd(t,r,!1);else{let i=n.get(o);Od(0,t,i,r),Od(1,t,i,r),Pd(t,r,!0)}}}function xd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),uh(t,i)}}function Od(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),uh(t,s)}}function uh(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Pd(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||_c(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Vv(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=St(o.type,!0)),s=new dr(i,Lt(o),je);e.blueprint[r]=s,n[r]=s,Uv(e,t,r,$f(e,n,o.hostVars,Je),o)}function Uv(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;$v(s)!=a&&s.push(a),s.push(n,r,i)}}function $v(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Hv(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Lt(t)&&(n[""]=e)}}function zv(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function lh(e,t,n,r,o,i,s,a){let c=t.consts,u=Bt(c,s),l=$c(t,e,2,r,u);return i&&ch(t,n,l,Bt(c,a),o),l.mergedAttrs=Ic(l.mergedAttrs,l.attrs),l.attrs!==null&&Ad(l,l.attrs,!1),l.mergedAttrs!==null&&Ad(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function dh(e,t){sf(e,t),Ma(t)&&e.queries.elementEnd(t)}function zc(e){return hh(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function fh(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function hh(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function ph(e,t,n){return e[t]=n}function gh(e,t){return e[t]}function pr(e,t,n){if(n===Je)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function fi(e,t,n,r){let o=pr(e,t,n);return pr(e,t+1,r)||o}function Gv(e,t,n,r,o,i){let s=fi(e,t,n,r);return fi(e,t+2,o,i)||s}function Qa(e,t,n){return function r(o){let i=ft(e)?Ce(e.index,t):t;Vc(i,5);let s=t[Y],a=kd(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=kd(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function kd(e,t,n,r){let o=R(null);try{return F(6,t,n),n(r)!==!1}catch(i){return Wy(e,i),!1}finally{F(7,t,n),R(o)}}function Wv(e,t,n,r,o,i,s,a){let c=Wo(e),u=!1,l=null;if(!r&&c&&(l=qv(t,n,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Fe(e,n),h=r?r(d):d;dy(n,h,i,a);let f=o.listen(h,i,a),g=r?m=>r(we(m[e.index])):e.index;mh(g,t,n,i,a,f,!1)}return u}function qv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[gn],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function mh(e,t,n,r,o,i,s){let a=t.firstCreatePass?Yl(t):null,c=Zl(n),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Fd(e,t,n,r,o,i){let s=t[n],a=t[N],u=a.data[n].outputs[r],d=s[u].subscribe(i);mh(e.index,a,t,o,i,d,!0)}var dc=Symbol("BINDING");var hi=class extends vr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=lt(t);return new wn(n,this.ngModule)}};function Zv(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Mi.SignalBased)!==0};return o&&(i.transform=o),i})}function Yv(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Qv(e,t,n){let r=t instanceof Z?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new lc(n,r):n}function Kv(e){let t=e.get(Ut,null);if(t===null)throw new v(407,!1);let n=e.get(ah,null),r=e.get(ut,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function Jv(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Lf(t,n,n==="svg"?Na:n==="math"?Hl:null)}var wn=class extends Ai{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Zv(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Yv(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Ty(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){F(22);let a=R(null);try{let c=this.componentDef,u=Xv(r,c,s,i),l=Qv(c,o||this.ngModule,t),d=Kv(l),h=d.rendererFactory.createRenderer(null,c),f=r?ky(h,r,c.encapsulation,l):Jv(c,h),g=s?.some(Ld)||i?.some(_=>typeof _!="function"&&_.bindings.some(Ld)),m=Mc(null,u,null,512|Uf(c),null,null,d,h,l,null,Mf(f,l,!0));m[Ie]=f,Ko(m);let E=null;try{let _=lh(Ie,u,m,"#host",()=>u.directiveRegistry,!0,0);f&&(Vf(h,f,_),mr(f,m)),Rc(u,m,_),Rf(u,_,m),dh(u,_),n!==void 0&&tD(_,this.ngContentSelectors,n),E=Ce(_.index,m),m[Y]=E[Y],xc(u,m,null)}catch(_){throw E!==null&&nc(E),nc(m),_}finally{F(23),Jo()}return new pi(this.componentType,m,!!g)}finally{R(a)}}};function Xv(e,t,n,r){let o=e?["ng-version","20.0.6"]:_y(t.selectors[0]),i=null,s=null,a=0;if(n)for(let l of n)a+=l[dc].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[dc].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[t];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=wa(d);c.push(h)}return Sc(0,null,eD(i,s),1,a,c,null,null,null,[o],null)}function eD(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Ld(e){let t=e[dc].kind;return t==="input"||t==="twoWay"}var pi=class extends sh{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=rr(n[N],Ie),this.location=Ci(this._tNode,n),this.instance=Ce(this._tNode.index,n)[Y],this.hostView=this.changeDetectorRef=new gt(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ac(r,o[N],o,t,n);this.previousInputValues.set(t,n);let s=Ce(r.index,o);Vc(s,1)}get injector(){return new Vt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function tD(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ht=(()=>{class e{static __NG_ELEMENT_ID__=nD}return e})();function nD(){let e=be();return oD(e,L())}var rD=Ht,yh=class extends rD{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Ci(this._hostTNode,this._hostLView)}get injector(){return new Vt(this._hostTNode,this._hostLView)}get parentInjector(){let t=wc(this._hostTNode,this._hostLView);if(cf(t)){let n=ci(t,this._hostLView),r=ai(t),o=n[N].data[r+8];return new Vt(o,n)}else return new Vt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=jd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ne}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=cc(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,di(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!Fm(t),u;if(c)u=n;else{let E=n||{};u=E.index,r=E.injector,o=E.projectableNodes,i=E.environmentInjector||E.ngModuleRef,s=E.directives,a=E.bindings}let l=c?t:new wn(lt(t)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let _=(c?d:this.parentInjector).get(Z,null);_&&(i=_)}let h=lt(l.componentType??{}),f=cc(this._lContainer,h?.id??null),g=f?.firstChild??null,m=l.create(d,o,g,i,s,a);return this.insertImpl(m.hostView,u,di(this._hostTNode,f)),m}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Gl(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[K],u=new yh(c,c[Ee],c[K]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Uc(s,o,i,r),t.attachToViewContainerRef(),va(Ka(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=jd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=hr(this._lContainer,n);r&&(Qn(Ka(this._lContainer),n),Ni(r[N],r))}detach(t){let n=this._adjustIndex(t,-1),r=hr(this._lContainer,n);return r&&Qn(Ka(this._lContainer),n)!=null?new gt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function jd(e){return e[tr]}function Ka(e){return e[tr]||(e[tr]=[])}function oD(e,t){let n,r=t[e.index];return Me(r)?n=r:(n=oh(r,t,null,e),t[e.index]=n,Nc(t,n)),sD(n,t,e,r),new yh(n,e,t)}function iD(e,t){let n=e[J],r=n.createComment(""),o=Fe(t,e),i=n.parentNode(o);return li(n,i,r,n.nextSibling(o),!1),r}var sD=uD,aD=()=>!1;function cD(e,t,n){return aD(e,t,n)}function uD(e,t,n,r){if(e[dt])return;let o;n.type&8?o=we(r):o=iD(t,n),e[dt]=o}var Bd=new Set;function Dr(e){Bd.has(e)||(Bd.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var $t=class{},xi=class{};var gi=class extends $t{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new hi(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Ia(t);this._bootstrapComponents=kf(i.bootstrap),this._r3Injector=Ha(t,n,[{provide:$t,useValue:this},{provide:vr,useValue:this.componentFactoryResolver},...r],pe(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},mi=class extends xi{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new gi(this.moduleType,t,[])}};var gr=class extends $t{injector;componentFactoryResolver=new hi(this);instance=null;constructor(t){super();let n=new Mt([...t.providers,{provide:$t,useValue:this},{provide:vr,useValue:this.componentFactoryResolver}],t.parent||Jn(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Er(e,t,n=null){return new gr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var lD=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Ca(!1,n.type),o=r.length>0?Er([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=I({token:e,providedIn:"environment",factory:()=>new e(w(Z))})}return e})();function Gc(e){return Cn(()=>{let t=vh(e),n=j(y({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Cc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(lD).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ke.Emulated,styles:e.styles||_e,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Dr("NgStandalone"),Dh(n);let r=e.dependencies;return n.directiveDefs=Vd(r,!1),n.pipeDefs=Vd(r,!0),n.id=gD(n),n})}function dD(e){return lt(e)||wa(e)}function fD(e){return e!==null}function Tn(e){return Cn(()=>({type:e.type,bootstrap:e.bootstrap||_e,declarations:e.declarations||_e,imports:e.imports||_e,exports:e.exports||_e,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function hD(e,t){if(e==null)return At;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Mi.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function pD(e){if(e==null)return At;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function zt(e){return Cn(()=>{let t=vh(e);return Dh(t),t})}function vh(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||At,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||_e,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:hD(e.inputs,t),outputs:pD(e.outputs),debugInfo:null}}function Dh(e){e.features?.forEach(t=>t(e))}function Vd(e,t){if(!e)return null;let n=t?kl:dD;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(fD)}function gD(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function mD(e,t,n,r,o,i,s,a,c){let u=t.consts,l=$c(t,e,4,s||null,a||null);Oa()&&ch(t,n,l,Bt(u,c),Wf),l.mergedAttrs=Ic(l.mergedAttrs,l.attrs),sf(t,l);let d=l.tView=Sc(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function fc(e,t,n,r,o,i,s,a,c,u,l){let d=n+Ie,h=t.firstCreatePass?mD(d,t,e,r,o,i,s,a,u):t.data[d];c&&(h.flags|=c),vn(h,!1);let f=yD(t,e,h,n);Xo()&&Lc(t,e,f,h),mr(f,e);let g=oh(f,e,f,h);return e[d]=g,Nc(e,g),cD(g,h,e),Wo(h)&&Rc(t,e,h),u!=null&&Gf(e,h,l),h}function Eh(e,t,n,r,o,i,s,a){let c=L(),u=Ye(),l=Bt(u.consts,i);return fc(c,u,e,t,n,r,o,l,void 0,s,a),Eh}var yD=vD;function vD(e,t,n,r){return ei(!0),t[J].createComment("")}var Wc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Wc||{}),Ir=new D(""),Ih=!1,hc=class extends ee{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Ul()&&(this.destroyRef=p(pt,{optional:!0})??void 0,this.pendingTasks=p(Qe,{optional:!0})??void 0)}emit(t){let n=R(null);try{super.next(t)}finally{R(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof V&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},oe=hc;function wh(e){let t,n;function r(){e=cr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ud(e){return queueMicrotask(()=>e()),()=>{e=cr}}var qc="isAngularZone",yi=qc+"_ID",DD=0,z=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new oe(!1);onMicrotaskEmpty=new oe(!1);onStable=new oe(!1);onError=new oe(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Ih}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,wD(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(qc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,ED,cr,cr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},ED={};function Zc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function ID(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){wh(()=>{e.callbackScheduled=!1,pc(e),e.isCheckStableRunning=!0,Zc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),pc(e)}function wD(e){let t=()=>{ID(e)},n=DD++;e._inner=e._inner.fork({name:"angular",properties:{[qc]:!0,[yi]:n,[yi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(CD(c))return r.invokeTask(i,s,a,c);try{return $d(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Hd(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return $d(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!bD(c)&&t(),Hd(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,pc(e),Zc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function pc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function $d(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Hd(e){e._nesting--,Zc(e)}var vi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new oe;onMicrotaskEmpty=new oe;onStable=new oe;onError=new oe;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function CD(e){return Ch(e,"__ignore_ng_zone__")}function bD(e){return Ch(e,"__scheduler_tick__")}function Ch(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var bh=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();var Yc=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Qc=new D("");function _n(e){return!!e&&typeof e.then=="function"}function Kc(e){return!!e&&typeof e.subscribe=="function"}var Th=new D("");var Jc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(Th,{optional:!0})??[];injector=p(xe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=re(this.injector,o);if(_n(i))n.push(i);else if(Kc(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Oi=new D("");function _h(){Os(()=>{let e="";throw new v(600,e)})}function Sh(e){return e.isBoundToModule}var TD=10;var Gt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(de);afterRenderManager=p(bh);zonelessEnabled=p(ar);rootEffectScheduler=p(Wa);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new ee;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(Qe);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(M(n=>!n))}constructor(){p(Ir,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(Z);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=xe.NULL){return this._injector.get(z).run(()=>{F(10);let s=n instanceof Ai;if(!this._injector.get(Jc).done){let g="";throw new v(405,g)}let c;s?c=n:c=this._injector.get(vr).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let u=Sh(c)?void 0:this._injector.get($t),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(Qc,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),lr(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),F(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){F(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Wc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=R(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,R(n),this.afterTick.next(),F(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Ut,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<TD;)F(14),this.synchronizeOnce(),F(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!or(o))continue;let i=r&&!this.zonelessEnabled?0:1;Bc(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>or(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;lr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Oi,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>lr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function lr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}var gc=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Ja(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function _D(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=Ja(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),f=t[c],g=Ja(s,h,c,f,n);if(g!==0){g<0&&e.updateValue(s,f),s--,c--;continue}let m=n(i,u),E=n(s,h),_=n(i,l);if(Object.is(_,E)){let Ae=n(c,f);Object.is(Ae,m)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new Di,o??=Gd(e,i,s,n),mc(e,r,i,_))e.updateValue(i,l),i++,s++;else if(o.has(_))r.set(m,e.detach(i)),s--;else{let Ae=e.create(i,t[i]);e.attach(i,Ae),i++,s++}}for(;i<=c;)zd(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,h=Ja(i,l,i,d,n);if(h!==0)h<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new Di,o??=Gd(e,i,s,n);let f=n(i,d);if(mc(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let g=n(i,l);r.set(g,e.detach(i)),s--}}}for(;!u.done;)zd(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function mc(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function zd(e,t,n,r,o){if(mc(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Gd(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Di=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};var yc=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-ne}};var vc=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function SD(e,t,n,r,o,i,s,a,c,u,l,d,h){Dr("NgControlFlow");let f=L(),g=Ye(),m=c!==void 0,E=L(),_=a?s.bind(E[ge][Y]):s,Ae=new vc(m,_);E[Ie+e]=Ae,fc(f,g,e+1,t,n,r,o,Bt(g.consts,i),256),m&&fc(f,g,e+2,c,u,l,d,Bt(g.consts,h),512)}var Dc=class extends gc{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-ne}at(t){return this.getLView(t)[Y].$implicit}attach(t,n){let r=n[pn];this.needsIndexUpdate||=t!==this.length,Uc(this.lContainer,n,t,di(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,ND(this.lContainer,t)}create(t,n){let r=cc(this.lContainer,this.templateTNode.tView.ssrId),o=Oc(this.hostLView,this.templateTNode,new yc(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){Ni(t[N],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[Y].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[Y].$index=t}getLView(t){return RD(this.lContainer,t)}};function MD(e){let t=R(null),n=Dn();try{let r=L(),o=r[N],i=r[n],s=n+1,a=Wd(r,s);if(i.liveCollection===void 0){let u=qd(o,s);i.liveCollection=new Dc(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(_D(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Yo(),l=c.length===0;if(pr(r,u,l)){let d=n+2,h=Wd(r,d);if(l){let f=qd(o,d),g=Pv(h,f,r),m=Oc(r,f,void 0,{dehydratedView:g});Uc(h,m,0,di(f,g))}else o.firstUpdatePass&&Rv(h),Cv(h,0)}}}finally{R(t)}}function Wd(e,t){return e[t]}function ND(e,t){return hr(e,t)}function RD(e,t){return wv(e,t)}function qd(e,t){return rr(e,t)}function Mh(e,t,n){let r=L(),o=Yo();if(pr(r,o,t)){let i=Ye(),s=pd();By(s,r,e,t,r[J],n)}return Mh}function Zd(e,t,n,r,o){Ac(t,e,n,o?"class":"style",r)}function Xc(e,t,n,r){let o=L(),i=Ye(),s=Ie+e,a=o[J],c=i.firstCreatePass?lh(s,i,o,t,Wf,Oa(),n,r):i.data[s],u=AD(i,o,c,a,t,e);o[s]=u;let l=Wo(c);return vn(c,!0),Vf(a,u,c),!kc(c)&&Xo()&&Lc(i,o,u,c),(Ql()===0||l)&&mr(u,o),Kl(),l&&(Rc(i,o,c),Rf(i,c,o)),r!==null&&Gf(o,c),Xc}function eu(){let e=be();ka()?rd():(e=e.parent,vn(e,!1));let t=e;ed(t)&&td(),Jl();let n=Ye();return n.firstCreatePass&&dh(n,t),t.classesWithoutHost!=null&&$m(t)&&Zd(n,t,L(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Hm(t)&&Zd(n,t,L(),t.stylesWithoutHost,!1),eu}function Pi(e,t,n,r){return Xc(e,t,n,r),eu(),Pi}var AD=(e,t,n,r,o,i)=>(ei(!0),Lf(r,o,yd()));var wr="en-US";var xD=wr;function Nh(e){typeof e=="string"&&(xD=e.toLowerCase().replace(/_/g,"-"))}function Rh(e,t,n){let r=L(),o=Ye(),i=be();return OD(o,r,r[J],i,e,t,n),Rh}function OD(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Qa(r,t,i),Wv(r,e,t,s,n,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=Qa(r,t,i),Fd(r,t,h,f,o,c)}if(u&&u.length)for(let d of u)c??=Qa(r,t,i),Fd(r,t,d,o,o,c)}}function PD(e=1){return hd(e)}function kD(e,t=""){let n=L(),r=Ye(),o=e+Ie,i=r.firstCreatePass?$c(r,o,1,t,null):r.data[o],s=FD(r,n,i,t,e);n[o]=s,Xo()&&Lc(r,n,s,i),vn(i,!1)}var FD=(e,t,n,r,o)=>(ei(!0),Sy(t[J],r));function LD(e,t,n,r=""){return pr(e,Yo(),n)?t+hn(n)+r:Je}function jD(e,t,n,r,o,i=""){let s=od(),a=fi(e,s,n,o);return sd(2),a?t+hn(n)+r+hn(o)+i:Je}function Ah(e){return tu("",e),Ah}function tu(e,t,n){let r=L(),o=LD(r,e,t,n);return o!==Je&&Oh(r,Dn(),o),tu}function xh(e,t,n,r,o){let i=L(),s=jD(i,e,t,n,r,o);return s!==Je&&Oh(i,Dn(),s),xh}function Oh(e,t,n){let r=zl(t,e);My(e[J],r,n)}function BD(e,t,n){let r=Ba()+e,o=L();return o[r]===Je?ph(o,r,n?t.call(n):t()):gh(o,r)}function VD(e,t,n,r,o,i,s,a,c){let u=Ba()+e,l=L(),d=Gv(l,u,n,r,o,i);return fi(l,u+4,s,a)||d?ph(l,u+6,c?t.call(c,n,r,o,i,s,a):t(n,r,o,i,s,a)):gh(l,u+6)}var Ei=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},nu=(()=>{class e{compileModuleSync(n){return new mi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Ia(n),i=kf(o.declarations).reduce((s,a)=>{let c=lt(a);return c&&s.push(new wn(c)),s},[]);return new Ei(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var UD=(()=>{class e{zone=p(z);changeDetectionScheduler=p(ut);applicationRef=p(Gt);applicationErrorHandler=p(de);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ph=new D("",{factory:()=>!1});function ru({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new z(j(y({},ou()),{scheduleInRootZone:n})),[{provide:z,useFactory:e},{provide:qe,multi:!0,useFactory:()=>{let r=p(UD,{optional:!0});return()=>r.initialize()}},{provide:qe,multi:!0,useFactory:()=>{let r=p(HD);return()=>{r.initialize()}}},t===!0?{provide:za,useValue:!0}:[],{provide:Ga,useValue:n??Ih},{provide:de,useFactory:()=>{let r=p(z),o=p(Z),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Oe),i.handleError(s))})}}}]}function $D(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=ru({ngZoneFactory:()=>{let o=ou(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Dr("NgZone_CoalesceEvent"),new z(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Ze([{provide:Ph,useValue:!0},{provide:ar,useValue:!1},r])}function ou(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var HD=(()=>{class e{subscription=new V;initialized=!1;zone=p(z);pendingTasks=p(Qe);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{z.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{z.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var kh=(()=>{class e{applicationErrorHandler=p(de);appRef=p(Gt);taskService=p(Qe);ngZone=p(z);zonelessEnabled=p(ar);tracing=p(Ir,{optional:!0});disableScheduling=p(za,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new V;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(yi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Ga,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof vi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ud:wh;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(yi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ud(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zD(){return typeof $localize<"u"&&$localize.locale||wr}var ki=new D("",{providedIn:"root",factory:()=>p(ki,{optional:!0,skipSelf:!0})||zD()});var Bh=Symbol("InputSignalNode#UNSET"),aE=j(y({},ks),{transformFn:void 0,applyValueToInputSignal(e,t){Ps(e,t)}});function Vh(e,t){let n=Object.create(aE);n.value=e,n.transformFn=t?.transform;function r(){if(Ns(n),n.value===Bh){let o=null;throw new v(-950,o)}return n.value}return r[no]=n,r}var cE=new D("");cE.__NG_ELEMENT_ID__=e=>{let t=be();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new v(204,!1)};function Fh(e,t){return Vh(e,t)}function uE(e){return Vh(Bh,e)}var Uh=(Fh.required=uE,Fh);var iu=new D(""),lE=new D("");function Cr(e){return!e.moduleRef}function dE(e){let t=Cr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(z);return n.run(()=>{Cr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(de),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Cr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(iu);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(iu);s.add(i),e.moduleRef.onDestroy(()=>{lr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return hE(r,n,()=>{let i=t.get(Jc);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(ki,wr);if(Nh(s||wr),!t.get(lE,!0))return Cr(e)?t.get(Gt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Cr(e)){let c=t.get(Gt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return fE?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var fE;function hE(e,t,n){try{let r=n();return _n(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Fi=null;function pE(e=[],t){return xe.create({name:t,providers:[{provide:Kn,useValue:"platform"},{provide:iu,useValue:new Set([()=>Fi=null])},...e]})}function gE(e=[]){if(Fi)return Fi;let t=pE(e);return Fi=t,_h(),mE(t),t}function mE(e){let t=e.get(_i,null);re(e,()=>{t?.forEach(n=>n())})}var lu=(()=>{class e{static __NG_ELEMENT_ID__=yE}return e})();function yE(e){return vE(be(),L(),(e&16)===16)}function vE(e,t,n){if(ft(e)&&!n){let r=Ce(e.index,t);return new gt(r,r)}else if(e.type&175){let r=t[ge];return new gt(r,t)}return null}var su=class{constructor(){}supports(t){return zc(t)}create(t){return new au(t)}},DE=(e,t)=>t,au=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||DE}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Lh(r,o,i)?n:r,a=Lh(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,g=f+h;l<=g&&g<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!zc(t))throw new v(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,fh(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new cu(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Li),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Li),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},cu=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},uu=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Li=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new uu,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Lh(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function jh(){return new du([new su])}var du=(()=>{class e{factories;static \u0275prov=I({token:e,providedIn:"root",factory:jh});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||jh()),deps:[[e,new ef,new Xd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new v(901,!1)}}return e})();function $h(e){F(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=gE(r),i=[ru({}),{provide:ut,useExisting:kh},Dd,...n||[]],s=new gr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return dE({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{F(9)}}var Gh=null;function Xe(){return Gh}function fu(e){Gh??=e}var br=class{},hu=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(Wh),providedIn:"platform"})}return e})();var Wh=(()=>{class e extends hu{_location;_history;_doc=p(H);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Xe().getBaseHref(this._doc)}onPopState(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function qh(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Hh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function yt(e){return e&&e[0]!=="?"?`?${e}`:e}var ji=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(Yh),providedIn:"root"})}return e})(),Zh=new D(""),Yh=(()=>{class e extends ji{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(H).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return qh(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+yt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+yt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+yt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(w(hu),w(Zh,8))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Sn=(()=>{class e{_subject=new ee;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=wE(Hh(zh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+yt(r))}normalize(n){return e.stripTrailingSlash(IE(this._basePath,zh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+yt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+yt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=yt;static joinWithSlash=qh;static stripTrailingSlash=Hh;static \u0275fac=function(r){return new(r||e)(w(ji))};static \u0275prov=I({token:e,factory:()=>EE(),providedIn:"root"})}return e})();function EE(){return new Sn(w(ji))}function IE(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function zh(e){return e.replace(/\/index.html$/,"")}function wE(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var pu=/\s+/,Qh=[],bE=(()=>{class e{_ngEl;_renderer;initialClasses=Qh;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(pu):Qh}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(pu):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(pu).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(je(bi),je(Hc))};static \u0275dir=zt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Bi=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Xh=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Bi(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Kh(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Kh(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(je(Ht),je(yr),je(du))};static \u0275dir=zt({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Kh(e,t){e.context.$implicit=t.item}var TE=(()=>{class e{_viewContainer;_context=new Vi;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Jh(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Jh(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(je(Ht),je(yr))};static \u0275dir=zt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Vi=class{$implicit=null;ngIf=null};function Jh(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}var ep=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Tn({type:e});static \u0275inj=Nt({})}return e})();function Tr(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var qt=class{};var tp="browser",_E="server";function np(e){return e===_E}var Hi=new D(""),vu=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(w(Hi),w(z))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),_r=class{_doc;constructor(t){this._doc=t}manager},Ui="ng-app-id";function rp(e){for(let t of e)t.remove()}function op(e,t){let n=t.createElement("style");return n.textContent=e,n}function SE(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Ui}="${t}"],link[${Ui}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Ui),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function mu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Du=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=np(i),SE(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,op);r?.forEach(o=>this.addUsage(o,this.external,mu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(rp(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])rp(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,op(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,mu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Ui,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(w(H),w(Ti),w(Si,8),w(bn))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),gu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Eu=/%COMP%/g;var sp="%COMP%",ME=`_nghost-${sp}`,NE=`_ngcontent-${sp}`,RE=!0,AE=new D("",{providedIn:"root",factory:()=>RE});function xE(e){return NE.replace(Eu,e)}function OE(e){return ME.replace(Eu,e)}function ap(e,t){return t.map(n=>n.replace(Eu,e))}var Iu=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Sr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof $i?o.applyToHost(n):o instanceof Mr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Ke.Emulated:i=new $i(c,u,r,this.appId,l,s,a,d,h);break;case Ke.ShadowDom:return new yu(c,u,n,r,s,a,this.nonce,d,h);default:i=new Mr(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(w(vu),w(Du),w(Ti),w(AE),w(H),w(bn),w(z),w(Si),w(Ir,8))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Sr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(gu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(ip(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(ip(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=gu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=gu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(mt.DashCase|mt.Important)?t.style.setProperty(n,r,o&mt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&mt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Xe().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function ip(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var yu=class extends Sr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=ap(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=mu(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Mr=class extends Sr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?ap(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},$i=class extends Mr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=xE(l),this.hostAttr=OE(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var zi=class e extends br{supportsDOMEvents=!0;static makeCurrent(){fu(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=PE();return n==null?null:kE(n)}resetBaseElement(){Nr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Tr(document.cookie,t)}},Nr=null;function PE(){return Nr=Nr||document.head.querySelector("base"),Nr?Nr.getAttribute("href"):null}function kE(e){return new URL(e,document.baseURI).pathname}var FE=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),up=(()=>{class e extends _r{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(w(H))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),cp=["alt","control","meta","shift"],LE={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},jE={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},lp=(()=>{class e extends _r{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Xe().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),cp.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=LE[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),cp.forEach(s=>{if(s!==o){let a=jE[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(w(H))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function BE(e,t){return $h(y({rootComponent:e},VE(t)))}function VE(e){return{appProviders:[...GE,...e?.providers??[]],platformProviders:zE}}function UE(){zi.makeCurrent()}function $E(){return new Oe}function HE(){return bc(document),document}var zE=[{provide:bn,useValue:tp},{provide:_i,useValue:UE,multi:!0},{provide:H,useFactory:HE}];var GE=[{provide:Kn,useValue:"root"},{provide:Oe,useFactory:$E},{provide:Hi,useClass:up,multi:!0,deps:[H]},{provide:Hi,useClass:lp,multi:!0,deps:[H]},Iu,Du,vu,{provide:Ut,useExisting:Iu},{provide:qt,useClass:FE},[]];var Nn=class{},Rr=class{},vt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Wi=class{encodeKey(t){return dp(t)}encodeValue(t){return dp(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function WE(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var qE=/%(\d[a-f0-9])/gi,ZE={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function dp(e){return encodeURIComponent(e).replace(qE,(t,n)=>ZE[n]??t)}function Gi(e){return`${e}`}var et=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Wi,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=WE(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Gi):[Gi(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Gi(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Gi(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var qi=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function YE(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function fp(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function hp(e){return typeof Blob<"u"&&e instanceof Blob}function pp(e){return typeof FormData<"u"&&e instanceof FormData}function QE(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var gp="Content-Type",mp="Accept",yp="X-Request-URL",vp="text/plain",Dp="application/json",KE=`${Dp}, ${vp}, */*`,Mn=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(YE(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new vt,this.context??=new qi,!this.params)this.params=new et,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||fp(this.body)||hp(this.body)||pp(this.body)||QE(this.body)?this.body:this.body instanceof et?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||pp(this.body)?null:hp(this.body)?this.body.type||null:fp(this.body)?null:typeof this.body=="string"?vp:this.body instanceof et?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Dp:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.keepalive??this.keepalive,s=t.transferCache??this.transferCache,a=t.body!==void 0?t.body:this.body,c=t.withCredentials??this.withCredentials,u=t.reportProgress??this.reportProgress,l=t.headers||this.headers,d=t.params||this.params,h=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((f,g)=>f.set(g,t.setHeaders[g]),l)),t.setParams&&(d=Object.keys(t.setParams).reduce((f,g)=>f.set(g,t.setParams[g]),d)),new e(n,r,a,{params:d,headers:l,context:h,reportProgress:u,responseType:o,withCredentials:c,transferCache:s,keepalive:i})}},Zt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Zt||{}),Rn=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new vt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Zi=class e extends Rn{constructor(t={}){super(t)}type=Zt.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Ar=class e extends Rn{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Zt.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},xr=class extends Rn{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},JE=200,XE=204;function wu(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive}}var Ep=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Mn)i=n;else{let c;o.headers instanceof vt?c=o.headers:c=new vt(o.headers);let u;o.params&&(o.params instanceof et?u=o.params:u=new et({fromObject:o.params})),i=new Mn(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive})}let s=C(i).pipe(st(c=>this.handler.handle(c)));if(n instanceof Mn||o.observe==="events")return s;let a=s.pipe(ce(c=>c instanceof Ar));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(M(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(M(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(M(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(M(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new et().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,wu(o,r))}post(n,r,o={}){return this.request("POST",n,wu(o,r))}put(n,r,o={}){return this.request("PUT",n,wu(o,r))}static \u0275fac=function(r){return new(r||e)(w(Nn))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var eI=new D("");function tI(e,t){return t(e)}function nI(e,t,n){return(r,o)=>re(n,()=>t(r,i=>e(i,o)))}var Ip=new D(""),wp=new D(""),Cp=new D("",{providedIn:"root",factory:()=>!0});var Yi=(()=>{class e extends Nn{backend;injector;chain=null;pendingTasks=p(ti);contributeToStability=p(Cp);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Ip),...this.injector.get(wp,[])]));this.chain=r.reduceRight((o,i)=>nI(o,i,this.injector),tI)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(bt(r))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(w(Rr),w(Z))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var rI=/^\)\]\}',?\n/,oI=RegExp(`^${yp}:`,"m");function iI(e){return"responseURL"in e&&e.responseURL?e.responseURL:oI.test(e.getAllResponseHeaders())?e.getResponseHeader(yp):null}var Cu=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);n.keepalive;let r=this.xhrFactory;return C(null).pipe(ue(()=>new A(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,E)=>s.setRequestHeader(m,E.join(","))),n.headers.has(mp)||s.setRequestHeader(mp,KE),!n.headers.has(gp)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(gp,m)}if(n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",E=new vt(s.getAllResponseHeaders()),_=iI(s)||n.url;return c=new Zi({headers:E,status:s.status,statusText:m,url:_}),c},l=()=>{let{headers:m,status:E,statusText:_,url:Ae}=u(),G=null;E!==XE&&(G=typeof s.response>"u"?s.responseText:s.response),E===0&&(E=G?JE:0);let Cs=E>=200&&E<300;if(n.responseType==="json"&&typeof G=="string"){let mg=G;G=G.replace(rI,"");try{G=G!==""?JSON.parse(G):null}catch(yg){G=mg,Cs&&(Cs=!1,G={error:yg,text:G})}}Cs?(i.next(new Ar({body:G,headers:m,status:E,statusText:_,url:Ae||void 0})),i.complete()):i.error(new xr({error:G,headers:m,status:E,statusText:_,url:Ae||void 0}))},d=m=>{let{url:E}=u(),_=new xr({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:E||void 0});i.error(_)},h=!1,f=m=>{h||(i.next(u()),h=!0);let E={type:Zt.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(E.total=m.total),n.responseType==="text"&&s.responseText&&(E.partialText=s.responseText),i.next(E)},g=m=>{let E={type:Zt.UploadProgress,loaded:m.loaded};m.lengthComputable&&(E.total=m.total),i.next(E)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",g)),s.send(a),i.next({type:Zt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",g)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(w(qt))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),bp=new D(""),sI="XSRF-TOKEN",aI=new D("",{providedIn:"root",factory:()=>sI}),cI="X-XSRF-TOKEN",uI=new D("",{providedIn:"root",factory:()=>cI}),Or=class{},lI=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Tr(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(w(H),w(aI))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function dI(e,t){let n=e.url.toLowerCase();if(!p(bp)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=p(Or).getToken(),o=p(uI);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}function fI(...e){let t=[Ep,Cu,Yi,{provide:Nn,useExisting:Yi},{provide:Rr,useFactory:()=>p(eI,{optional:!0})??p(Cu)},{provide:Ip,useValue:dI,multi:!0},{provide:bp,useValue:!0},{provide:Or,useClass:lI}];for(let n of e)t.push(...n.\u0275providers);return Ze(t)}var Tp=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(w(H))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var S="primary",Zr=Symbol("RouteTitle"),Mu=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Kt(e){return new Mu(e)}function Op(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function pI(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Be(e[n],t[n]))return!1;return!0}function Be(e,t){let n=e?Nu(e):void 0,r=t?Nu(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Pp(e[o],t[o]))return!1;return!0}function Nu(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Pp(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function kp(e){return e.length>0?e[e.length-1]:null}function rt(e){return Gs(e)?e:_n(e)?$(Promise.resolve(e)):C(e)}var gI={exact:Lp,subset:jp},Fp={exact:mI,subset:yI,ignored:()=>!0};function _p(e,t,n){return gI[n.paths](e.root,t.root,n.matrixParams)&&Fp[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function mI(e,t){return Be(e,t)}function Lp(e,t,n){if(!Yt(e.segments,t.segments)||!Ji(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Lp(e.children[r],t.children[r],n))return!1;return!0}function yI(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Pp(e[n],t[n]))}function jp(e,t,n){return Bp(e,t,t.segments,n)}function Bp(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Yt(o,n)||t.hasChildren()||!Ji(o,n,r))}else if(e.segments.length===n.length){if(!Yt(e.segments,n)||!Ji(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!jp(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Yt(e.segments,o)||!Ji(e.segments,o,r)||!e.children[S]?!1:Bp(e.children[S],t,i,r)}}function Ji(e,t,n){return t.every((r,o)=>Fp[n](e[o].parameters,r.parameters))}var Ue=class{root;queryParams;fragment;_queryParamMap;constructor(t=new k([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Kt(this.queryParams),this._queryParamMap}toString(){return EI.serialize(this)}},k=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Xi(this)}},Dt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Kt(this.parameters),this._parameterMap}toString(){return Up(this)}};function vI(e,t){return Yt(e,t)&&e.every((n,r)=>Be(n.parameters,t[r].parameters))}function Yt(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function DI(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===S&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==S&&(n=n.concat(t(o,r)))}),n}var Yr=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new Jt,providedIn:"root"})}return e})(),Jt=class{parse(t){let n=new Au(t);return new Ue(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Pr(t.root,!0)}`,r=CI(t.queryParams),o=typeof t.fragment=="string"?`#${II(t.fragment)}`:"";return`${n}${r}${o}`}},EI=new Jt;function Xi(e){return e.segments.map(t=>Up(t)).join("/")}function Pr(e,t){if(!e.hasChildren())return Xi(e);if(t){let n=e.children[S]?Pr(e.children[S],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==S&&r.push(`${o}:${Pr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=DI(e,(r,o)=>o===S?[Pr(e.children[S],!1)]:[`${o}:${Pr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[S]!=null?`${Xi(e)}/${n[0]}`:`${Xi(e)}/(${n.join("//")})`}}function Vp(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Qi(e){return Vp(e).replace(/%3B/gi,";")}function II(e){return encodeURI(e)}function Ru(e){return Vp(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function es(e){return decodeURIComponent(e)}function Sp(e){return es(e.replace(/\+/g,"%20"))}function Up(e){return`${Ru(e.path)}${wI(e.parameters)}`}function wI(e){return Object.entries(e).map(([t,n])=>`;${Ru(t)}=${Ru(n)}`).join("")}function CI(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Qi(n)}=${Qi(o)}`).join("&"):`${Qi(n)}=${Qi(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var bI=/^[^\/()?;#]+/;function bu(e){let t=e.match(bI);return t?t[0]:""}var TI=/^[^\/()?;=#]+/;function _I(e){let t=e.match(TI);return t?t[0]:""}var SI=/^[^=?&#]+/;function MI(e){let t=e.match(SI);return t?t[0]:""}var NI=/^[^&#]+/;function RI(e){let t=e.match(NI);return t?t[0]:""}var Au=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new k([],{}):new k([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[S]=new k(t,n)),r}parseSegment(){let t=bu(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new Dt(es(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=_I(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=bu(this.remaining);o&&(r=o,this.capture(r))}t[es(n)]=es(r)}parseQueryParam(t){let n=MI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=RI(this.remaining);s&&(r=s,this.capture(r))}let o=Sp(n),i=Sp(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=bu(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=S);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[S]:new k([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function $p(e){return e.segments.length>0?new k([],{[S]:e}):e}function Hp(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Hp(o);if(r===S&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new k(e.segments,t);return AI(n)}function AI(e){if(e.numberOfChildren===1&&e.children[S]){let t=e.children[S];return new k(e.segments.concat(t.segments),t.children)}return e}function kn(e){return e instanceof Ue}function zp(e,t,n=null,r=null){let o=Gp(e);return Wp(o,t,n,r)}function Gp(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new k(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=$p(r);return t??o}function Wp(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Tu(o,o,o,n,r);let i=xI(t);if(i.toRoot())return Tu(o,o,new k([],{}),n,r);let s=OI(i,o,e),a=s.processChildren?Fr(s.segmentGroup,s.index,i.commands):Zp(s.segmentGroup,s.index,i.commands);return Tu(o,s.segmentGroup,a,n,r)}function ts(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Br(e){return typeof e=="object"&&e!=null&&e.outlets}function Tu(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=qp(e,t,n);let a=$p(Hp(s));return new Ue(a,i,o)}function qp(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=qp(i,t,n)}),new k(e.segments,r)}var ns=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&ts(r[0]))throw new v(4003,!1);let o=r.find(Br);if(o&&o!==kp(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function xI(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ns(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ns(n,t,r)}var On=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function OI(e,t,n){if(e.isAbsolute)return new On(t,!0,0);if(!n)return new On(t,!1,NaN);if(n.parent===null)return new On(n,!0,0);let r=ts(e.commands[0])?0:1,o=n.segments.length-1+r;return PI(n,o,e.numberOfDoubleDots)}function PI(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new On(r,!1,o-i)}function kI(e){return Br(e[0])?e[0].outlets:{[S]:e}}function Zp(e,t,n){if(e??=new k([],{}),e.segments.length===0&&e.hasChildren())return Fr(e,t,n);let r=FI(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new k(e.segments.slice(0,r.pathIndex),{});return i.children[S]=new k(e.segments.slice(r.pathIndex),e.children),Fr(i,0,o)}else return r.match&&o.length===0?new k(e.segments,{}):r.match&&!e.hasChildren()?xu(e,t,n):r.match?Fr(e,0,o):xu(e,t,n)}function Fr(e,t,n){if(n.length===0)return new k(e.segments,{});{let r=kI(n),o={};if(Object.keys(r).some(i=>i!==S)&&e.children[S]&&e.numberOfChildren===1&&e.children[S].segments.length===0){let i=Fr(e.children[S],t,n);return new k(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Zp(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new k(e.segments,o)}}function FI(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Br(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Np(c,u,s))return i;r+=2}else{if(!Np(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function xu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Br(i)){let c=LI(i.outlets);return new k(r,c)}if(o===0&&ts(n[0])){let c=e.segments[t];r.push(new Dt(c.path,Mp(n[0]))),o++;continue}let s=Br(i)?i.outlets[S]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&ts(a)?(r.push(new Dt(s,Mp(a))),o+=2):(r.push(new Dt(s,{})),o++)}return new k(r,{})}function LI(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=xu(new k([],{}),0,r))}),t}function Mp(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Np(e,t,n){return e==n.path&&Be(t,n.parameters)}var Lr="imperative",X=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(X||{}),ve=class{id;url;constructor(t,n){this.id=t,this.url=n}},Xt=class extends ve{type=X.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},tt=class extends ve{urlAfterRedirects;type=X.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},ie=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(ie||{}),Vr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Vr||{}),Ve=class extends ve{reason;code;type=X.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},nt=class extends ve{reason;code;type=X.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Fn=class extends ve{error;target;type=X.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Ur=class extends ve{urlAfterRedirects;state;type=X.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},rs=class extends ve{urlAfterRedirects;state;type=X.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},os=class extends ve{urlAfterRedirects;state;shouldActivate;type=X.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},is=class extends ve{urlAfterRedirects;state;type=X.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ss=class extends ve{urlAfterRedirects;state;type=X.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},as=class{route;type=X.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},cs=class{route;type=X.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},us=class{snapshot;type=X.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ls=class{snapshot;type=X.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ds=class{snapshot;type=X.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},fs=class{snapshot;type=X.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var $r=class{},Ln=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function jI(e){return!(e instanceof $r)&&!(e instanceof Ln)}function BI(e,t){return e.providers&&!e._injector&&(e._injector=Er(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Re(e){return e.outlet||S}function VI(e,t){let n=e.filter(r=>Re(r)===t);return n.push(...e.filter(r=>Re(r)!==t)),n}function Qr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var hs=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Qr(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Vn(this.rootInjector)}},Vn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new hs(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(w(Z))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ps=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Ou(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Ou(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Pu(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Pu(t,this._root).map(n=>n.value)}};function Ou(e,t){if(e===t.value)return t;for(let n of t.children){let r=Ou(e,n);if(r)return r}return null}function Pu(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Pu(e,n);if(r.length)return r.unshift(t),r}return[]}var ye=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function xn(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Hr=class extends ps{snapshot;constructor(t,n){super(t),this.snapshot=n,$u(this,t)}toString(){return this.snapshot.toString()}};function Yp(e){let t=UI(e),n=new te([new Dt("",{})]),r=new te({}),o=new te({}),i=new te({}),s=new te(""),a=new Et(n,r,i,s,o,S,e,t.root);return a.snapshot=t.root,new Hr(new ye(a,[]),t)}function UI(e){let t={},n={},r={},o="",i=new Qt([],t,r,o,n,S,e,null,{});return new zr("",new ye(i,[]))}var Et=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(M(u=>u[Zr]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(M(t=>Kt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(M(t=>Kt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function gs(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:y(y({},t.params),e.params),data:y(y({},t.data),e.data),resolve:y(y(y(y({},e.data),t.data),o?.data),e._resolvedData)}:r={params:y({},e.params),data:y({},e.data),resolve:y(y({},e.data),e._resolvedData??{})},o&&Kp(o)&&(r.resolve[Zr]=o.title),r}var Qt=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Zr]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Kt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Kt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},zr=class extends ps{url;constructor(t,n){super(n),this.url=t,$u(this,n)}toString(){return Qp(this._root)}};function $u(e,t){t.value._routerState=e,t.children.forEach(n=>$u(e,n))}function Qp(e){let t=e.children.length>0?` { ${e.children.map(Qp).join(", ")} } `:"";return`${e.value}${t}`}function _u(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Be(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Be(t.params,n.params)||e.paramsSubject.next(n.params),pI(t.url,n.url)||e.urlSubject.next(n.url),Be(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function ku(e,t){let n=Be(e.params,t.params)&&vI(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||ku(e.parent,t.parent))}function Kp(e){return typeof e.title=="string"||e.title===null}var Jp=new D(""),Hu=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=S;activateEvents=new oe;deactivateEvents=new oe;attachEvents=new oe;detachEvents=new oe;routerOutletData=Uh(void 0);parentContexts=p(Vn);location=p(Ht);changeDetector=p(lu);inputBinder=p(Ds,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Fu(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=zt({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ii]})}return e})(),Fu=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Et?this.route:t===Vn?this.childContexts:t===Jp?this.outletData:this.parent.get(t,n)}},Ds=new D("");var zu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Gc({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Pi(0,"router-outlet")},dependencies:[Hu],encapsulation:2})}return e})();function Gu(e){let t=e.children&&e.children.map(Gu),n=t?j(y({},e),{children:t}):y({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==S&&(n.component=zu),n}function $I(e,t,n){let r=Gr(e,t._root,n?n._root:void 0);return new Hr(r,t)}function Gr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=HI(e,t,n);return new ye(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Gr(e,a)),s}}let r=zI(t.value),o=t.children.map(i=>Gr(e,i));return new ye(r,o)}}function HI(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Gr(e,r,o);return Gr(e,r)})}function zI(e){return new Et(new te(e.url),new te(e.params),new te(e.queryParams),new te(e.fragment),new te(e.data),e.outlet,e.component,e)}var jn=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Xp="ngNavigationCancelingError";function ms(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=kn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=eg(!1,ie.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function eg(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Xp]=!0,n.cancellationCode=t,n}function GI(e){return tg(e)&&kn(e.url)}function tg(e){return!!e&&e[Xp]}var WI=(e,t,n,r)=>M(o=>(new Lu(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Lu=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),_u(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=xn(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=xn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=xn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=xn(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new fs(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ls(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(_u(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),_u(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},ys=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Pn=class{component;route;constructor(t,n){this.component=t,this.route=n}};function qI(e,t,n){let r=e._root,o=t?t._root:null;return kr(r,o,n,[r.value])}function ZI(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Un(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!ua(e)?e:t.get(e):r}function kr(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=xn(t);return e.children.forEach(s=>{YI(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>jr(a,n.getContext(s),o)),o}function YI(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=QI(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new ys(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?kr(e,t,a?a.children:null,r,o):kr(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Pn(a.outlet.component,s))}else s&&jr(t,a,o),o.canActivateChecks.push(new ys(r)),i.component?kr(e,null,a?a.children:null,r,o):kr(e,null,n,r,o);return o}function QI(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Yt(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Yt(e.url,t.url)||!Be(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ku(e,t)||!Be(e.queryParams,t.queryParams);case"paramsChange":default:return!ku(e,t)}}function jr(e,t,n){let r=xn(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?jr(s,t.children.getContext(i),n):jr(s,null,n):jr(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Pn(t.outlet.component,o)):n.canDeactivateChecks.push(new Pn(null,o)):n.canDeactivateChecks.push(new Pn(null,o))}function Kr(e){return typeof e=="function"}function KI(e){return typeof e=="boolean"}function JI(e){return e&&Kr(e.canLoad)}function XI(e){return e&&Kr(e.canActivate)}function ew(e){return e&&Kr(e.canActivateChild)}function tw(e){return e&&Kr(e.canDeactivate)}function nw(e){return e&&Kr(e.canMatch)}function ng(e){return e instanceof He||e?.name==="EmptyError"}var Ki=Symbol("INITIAL_VALUE");function Bn(){return ue(e=>Ro(e.map(t=>t.pipe(ze(1),Zs(Ki)))).pipe(M(t=>{for(let n of t)if(n!==!0){if(n===Ki)return Ki;if(n===!1||rw(n))return n}return!0}),ce(t=>t!==Ki),ze(1)))}function rw(e){return kn(e)||e instanceof jn}function ow(e,t){return q(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(j(y({},n),{guardsResult:!0})):iw(s,r,o,e).pipe(q(a=>a&&KI(a)?sw(r,i,e,t):C(a)),M(a=>j(y({},n),{guardsResult:a})))})}function iw(e,t,n,r){return $(e).pipe(q(o=>dw(o.component,o.route,n,t,r)),Ge(o=>o!==!0,!0))}function sw(e,t,n,r){return $(t).pipe(st(o=>ln(cw(o.route.parent,r),aw(o.route,r),lw(e,o.path,n),uw(e,o.route,n))),Ge(o=>o!==!0,!0))}function aw(e,t){return e!==null&&t&&t(new ds(e)),C(!0)}function cw(e,t){return e!==null&&t&&t(new us(e)),C(!0)}function uw(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>Gn(()=>{let s=Qr(t)??n,a=Un(i,s),c=XI(a)?a.canActivate(t,e):re(s,()=>a(t,e));return rt(c).pipe(Ge())}));return C(o).pipe(Bn())}function lw(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>ZI(s)).filter(s=>s!==null).map(s=>Gn(()=>{let a=s.guards.map(c=>{let u=Qr(s.node)??n,l=Un(c,u),d=ew(l)?l.canActivateChild(r,e):re(u,()=>l(r,e));return rt(d).pipe(Ge())});return C(a).pipe(Bn())}));return C(i).pipe(Bn())}function dw(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=Qr(t)??o,u=Un(a,c),l=tw(u)?u.canDeactivate(e,t,n,r):re(c,()=>u(e,t,n,r));return rt(l).pipe(Ge())});return C(s).pipe(Bn())}function fw(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=Un(s,e),c=JI(a)?a.canLoad(t,n):re(e,()=>a(t,n));return rt(c)});return C(i).pipe(Bn(),rg(r))}function rg(e){return Us(Q(t=>{if(typeof t!="boolean")throw ms(e,t)}),M(t=>t===!0))}function hw(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=Un(s,e),c=nw(a)?a.canMatch(t,n):re(e,()=>a(t,n));return rt(c)});return C(i).pipe(Bn(),rg(r))}var Wr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},qr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function An(e){return un(new Wr(e))}function pw(e){return un(new v(4e3,!1))}function gw(e){return un(eg(!1,ie.GuardRejected))}var ju=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[S])return pw(`${t.redirectTo}`);o=o.children[S]}}applyRedirectCommands(t,n,r,o,i){return mw(n,o,i).pipe(M(s=>{if(s instanceof Ue)throw new qr(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,r);if(s[0]==="/")throw new qr(a);return a}))}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Ue(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new k(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}};function mw(e,t,n){if(typeof e=="string")return C(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=t;return rt(re(n,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var Bu={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function yw(e,t,n,r,o){let i=og(e,t,n);return i.matched?(r=BI(t,r),hw(r,t,n,o).pipe(M(s=>s===!0?i:y({},Bu)))):C(i)}function og(e,t,n){if(t.path==="**")return vw(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?y({},Bu):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||Op)(n,e,t);if(!o)return y({},Bu);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?y(y({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function vw(e){return{matched:!0,parameters:e.length>0?kp(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Rp(e,t,n,r){return n.length>0&&Iw(e,n,r)?{segmentGroup:new k(t,Ew(r,new k(n,e.children))),slicedSegments:[]}:n.length===0&&ww(e,n,r)?{segmentGroup:new k(e.segments,Dw(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new k(e.segments,e.children),slicedSegments:n}}function Dw(e,t,n,r){let o={};for(let i of n)if(Es(e,t,i)&&!r[Re(i)]){let s=new k([],{});o[Re(i)]=s}return y(y({},r),o)}function Ew(e,t){let n={};n[S]=t;for(let r of e)if(r.path===""&&Re(r)!==S){let o=new k([],{});n[Re(r)]=o}return n}function Iw(e,t,n){return n.some(r=>Es(e,t,r)&&Re(r)!==S)}function ww(e,t,n){return n.some(r=>Es(e,t,r))}function Es(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function Cw(e,t,n){return t.length===0&&!e.children[n]}var Vu=class{};function bw(e,t,n,r,o,i,s="emptyOnly"){return new Uu(e,t,n,r,o,s,i).recognize()}var Tw=31,Uu=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new ju(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=Rp(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(M(({children:n,rootSnapshot:r})=>{let o=new ye(r,n),i=new zr("",o),s=zp(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Qt([],Object.freeze({}),Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),S,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,S,n).pipe(M(r=>({children:r,rootSnapshot:n})),it(r=>{if(r instanceof qr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Wr?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(M(s=>s instanceof ye?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return $(i).pipe(st(s=>{let a=r.children[s],c=VI(n,s);return this.processSegmentGroup(t,c,a,s,o)}),qs((s,a)=>(s.push(...a),s)),at(null),Ws(),q(s=>{if(s===null)return An(r);let a=ig(s);return _w(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return $(n).pipe(st(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(it(u=>{if(u instanceof Wr)return C(null);throw u}))),Ge(c=>!!c),it(c=>{if(ng(c))return Cw(r,o,i)?C(new Vu):An(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Re(r)!==s&&(s===S||!Es(o,i,r))?An(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):An(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=og(n,o,i);if(!c)return An(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Tw&&(this.allowRedirects=!1));let f=new Qt(i,u,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Ap(o),Re(o),o.component??o._loadedComponent??null,o,xp(o)),g=gs(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(g.params),f.data=Object.freeze(g.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t).pipe(ue(E=>this.applyRedirects.lineralizeSegments(o,E)),q(E=>this.processSegment(t,r,n,E.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=yw(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(ue(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(ue(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,g=new Qt(h,d,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Ap(r),Re(r),r.component??r._loadedComponent??null,r,xp(r)),m=gs(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(m.params),g.data=Object.freeze(m.data);let{segmentGroup:E,slicedSegments:_}=Rp(n,h,f,u);if(_.length===0&&E.hasChildren())return this.processChildren(l,u,E,g).pipe(M(G=>new ye(g,G)));if(u.length===0&&_.length===0)return C(new ye(g,[]));let Ae=Re(r)===i;return this.processSegment(l,u,E,_,Ae?S:i,!0,g).pipe(M(G=>new ye(g,G instanceof ye?[G]:[])))}))):An(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):fw(t,n,r,this.urlSerializer).pipe(q(o=>o?this.configLoader.loadChildren(t,n).pipe(Q(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):gw(n))):C({routes:[],injector:t})}};function _w(e){e.sort((t,n)=>t.value.outlet===S?-1:n.value.outlet===S?1:t.value.outlet.localeCompare(n.value.outlet))}function Sw(e){let t=e.value.routeConfig;return t&&t.path===""}function ig(e){let t=[],n=new Set;for(let r of e){if(!Sw(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=ig(r.children);t.push(new ye(r.value,o))}return t.filter(r=>!n.has(r))}function Ap(e){return e.data||{}}function xp(e){return e.resolve||{}}function Mw(e,t,n,r,o,i){return q(s=>bw(e,t,n,r,s.extractedUrl,o,i).pipe(M(({state:a,tree:c})=>j(y({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function Nw(e,t){return q(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of sg(c))s.add(u);let a=0;return $(s).pipe(st(c=>i.has(c)?Rw(c,r,e,t):(c.data=gs(c,c.parent,e).resolve,C(void 0))),Q(()=>a++),dn(1),q(c=>a===s.size?C(n):se))})}function sg(e){let t=e.children.map(n=>sg(n)).flat();return[e,...t]}function Rw(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Kp(o)&&(i[Zr]=o.title),Gn(()=>(e.data=gs(e,e.parent,n).resolve,Aw(i,e,t,r).pipe(M(s=>(e._resolvedData=s,e.data=y(y({},e.data),s),null)))))}function Aw(e,t,n,r){let o=Nu(e);if(o.length===0)return C({});let i={};return $(o).pipe(q(s=>xw(e[s],t,n,r).pipe(Ge(),Q(a=>{if(a instanceof jn)throw ms(new Jt,a);i[s]=a}))),dn(1),M(()=>i),it(s=>ng(s)?se:un(s)))}function xw(e,t,n,r){let o=Qr(t)??r,i=Un(e,o),s=i.resolve?i.resolve(t,n):re(o,()=>i(t,n));return rt(s)}function Su(e){return ue(t=>{let n=e(t);return n?$(n).pipe(M(()=>t)):C(t)})}var Wu=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===S);return r}getResolvedTitleForRoute(n){return n.data[Zr]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(ag),providedIn:"root"})}return e})(),ag=(()=>{class e extends Wu{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(w(Tp))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Jr=new D("",{providedIn:"root",factory:()=>({})}),Xr=new D(""),cg=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(nu);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=rt(n.loadComponent()).pipe(M(lg),Q(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),bt(()=>{this.componentLoaders.delete(n)})),o=new cn(r,()=>new ee).pipe(an());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=ug(r,this.compiler,n,this.onLoadEndListener).pipe(bt(()=>{this.childrenLoaders.delete(r)})),s=new cn(i,()=>new ee).pipe(an());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ug(e,t,n,r){return rt(e.loadChildren()).pipe(M(lg),q(o=>o instanceof xi||Array.isArray(o)?C(o):$(t.compileModuleAsync(o))),M(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Xr,[],{optional:!0,self:!0}).flat()),{routes:s.map(Gu),injector:i}}))}function Ow(e){return e&&typeof e=="object"&&"default"in e}function lg(e){return Ow(e)?e.default:e}var Is=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(Pw),providedIn:"root"})}return e})(),Pw=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dg=new D("");var fg=new D(""),hg=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new ee;transitionAbortWithErrorSubject=new ee;configLoader=p(cg);environmentInjector=p(Z);destroyRef=p(pt);urlSerializer=p(Yr);rootContexts=p(Vn);location=p(Sn);inputBindingEnabled=p(Ds,{optional:!0})!==null;titleStrategy=p(Wu);options=p(Jr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Is);createViewTransition=p(dg,{optional:!0});navigationErrorHandler=p(fg,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new as(o)),r=o=>this.events.next(new cs(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(y({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(n){return this.transitions=new te(null),this.transitions.pipe(ce(r=>r!==null),ue(r=>{let o=!1;return C(r).pipe(ue(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",ie.SupersededByNewNavigation),se;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?j(y({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new nt(i.id,this.urlSerializer.serialize(i.rawUrl),c,Vr.IgnoredSameUrlNavigation)),i.resolve(!1),se}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return C(i).pipe(ue(c=>(this.events.next(new Xt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?se:Promise.resolve(c))),Mw(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Q(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=j(y({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new Ur(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Xt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let g=Yp(this.rootComponentType).snapshot;return this.currentTransition=r=j(y({},i),{targetSnapshot:g,urlAfterRedirects:u,extras:j(y({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,C(r)}else{let c="";return this.events.next(new nt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Vr.IgnoredByUrlHandlingStrategy)),i.resolve(!1),se}}),Q(i=>{let s=new rs(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),M(i=>(this.currentTransition=r=j(y({},i),{guards:qI(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),ow(this.environmentInjector,i=>this.events.next(i)),Q(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw ms(this.urlSerializer,i.guardsResult);let s=new os(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),ce(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",ie.GuardRejected),!1)),Su(i=>{if(i.guards.canActivateChecks.length!==0)return C(i).pipe(Q(s=>{let a=new is(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),ue(s=>{let a=!1;return C(s).pipe(Nw(this.paramsInheritanceStrategy,this.environmentInjector),Q({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",ie.NoDataFromResolver)}}))}),Q(s=>{let a=new ss(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Su(i=>{let s=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(Q(u=>{a.component=u}),M(()=>{})));for(let u of a.children)c.push(...s(u));return c};return Ro(s(i.targetSnapshot.root)).pipe(at(null),ze(1))}),Su(()=>this.afterPreactivation()),ue(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?$(a).pipe(M(()=>r)):C(r)}),M(i=>{let s=$I(n.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=j(y({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),Q(()=>{this.events.next(new $r)}),WI(this.rootContexts,n.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),ze(1),xo(new A(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(ce(()=>!o&&!r.targetRouterState),Q(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",ie.Aborted)}))),Q({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new tt(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),xo(this.transitionAbortWithErrorSubject.pipe(Q(i=>{throw i}))),bt(()=>{o||this.cancelNavigationTransition(r,"",ie.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),it(i=>{if(this.destroyed)return r.resolve(!1),se;if(o=!0,tg(i))this.events.next(new Ve(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),GI(i)?this.events.next(new Ln(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Fn(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=re(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof jn){let{message:c,cancellationCode:u}=ms(this.urlSerializer,a);this.events.next(new Ve(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new Ln(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return se}))}))}cancelNavigationTransition(n,r,o){let i=new Ve(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function kw(e){return e!==Lr}var pg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(Fw),providedIn:"root"})}return e})(),vs=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},Fw=(()=>{class e extends vs{static \u0275fac=(()=>{let n;return function(o){return(n||(n=wi(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),gg=(()=>{class e{urlSerializer=p(Yr);options=p(Jr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Sn);urlHandlingStrategy=p(Is);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Ue;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Ue?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Yp(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>p(Lw),providedIn:"root"})}return e})(),Lw=(()=>{class e extends gg{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Xt?this.updateStateMemento():n instanceof nt?this.commitTransition(r):n instanceof Ur?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof $r?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Ve&&n.code!==ie.SupersededByNewNavigation&&n.code!==ie.Redirect?this.restoreHistory(r):n instanceof Fn?this.restoreHistory(r,!0):n instanceof tt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=y(y({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=y(y({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=wi(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function qu(e,t){e.events.pipe(ce(n=>n instanceof tt||n instanceof Ve||n instanceof Fn||n instanceof nt),M(n=>n instanceof tt||n instanceof nt?0:(n instanceof Ve?n.code===ie.Redirect||n.code===ie.SupersededByNewNavigation:!1)?2:1),ce(n=>n!==2),ze(1)).subscribe(()=>{t()})}var jw={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Bw={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ws=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Yc);stateManager=p(gg);options=p(Jr,{optional:!0})||{};pendingTasks=p(Qe);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(hg);urlSerializer=p(Yr);location=p(Sn);urlHandlingStrategy=p(Is);injector=p(Z);_events=new ee;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(pg);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Xr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Ds,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new V;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Ve&&r.code!==ie.Redirect&&r.code!==ie.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof tt)this.navigated=!0;else if(r instanceof Ln){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=y({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||kw(o.source)},s);this.scheduleNavigation(a,Lr,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}jI(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Lr,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=y({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i).catch(c=>{this.injector.get(de)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Gu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=y(y({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=Gp(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Wp(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=kn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Lr,null,r)}navigate(n,r={skipLocationChange:!1}){return Vw(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=y({},jw):r===!1?o=y({},Bw):o=r,kn(n))return _p(this.currentUrlTree,n,o);let i=this.parseUrl(n);return _p(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return qu(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Vw(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}var $w=new D("");function Hw(e,...t){return Ze([{provide:Xr,multi:!0,useValue:e},[],{provide:Et,useFactory:zw,deps:[ws]},{provide:Oi,multi:!0,useFactory:Gw},t.map(n=>n.\u0275providers)])}function zw(e){return e.routerState.root}function Gw(){let e=p(xe);return t=>{let n=e.get(Gt);if(t!==n.components[0])return;let r=e.get(ws),o=e.get(Ww);e.get(qw)===1&&r.initialNavigation(),e.get(Zw,null,{optional:!0})?.setUpPreloading(),e.get($w,null,{optional:!0})?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Ww=new D("",{factory:()=>new ee}),qw=new D("",{providedIn:"root",factory:()=>1});var Zw=new D("");export{y as a,j as b,M as c,Yg as d,ue as e,I as f,w as g,gd as h,md as i,bm as j,py as k,Py as l,je as m,Gc as n,Eh as o,SD as p,MD as q,Mh as r,Xc as s,eu as t,Pi as u,Rh as v,PD as w,kD as x,Ah as y,tu as z,xh as A,BD as B,VD as C,$D as D,bE as E,Xh as F,TE as G,ep as H,BE as I,Ep as J,fI as K,Hu as L,ws as M,Hw as N};
