.ingredients-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.logo {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.nav-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.admin-button {
  background: linear-gradient(45deg, #28a745, #20c997) !important;
}

.admin-button:hover {
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4) !important;
}

.loading, .error, .no-ingredients {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  text-align: center;
}

.ingredients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.ingredient-card {
  background: white;
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.ingredient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.ingredient-info {
  flex: 1;
}

.ingredient-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.ingredient-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.fodmap-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  white-space: nowrap;
}

.fodmap-low {
  border-left-color: #28a745;
}

.fodmap-low .fodmap-badge {
  background: #d4edda;
  color: #155724;
}

.fodmap-moderate {
  border-left-color: #ffc107;
}

.fodmap-moderate .fodmap-badge {
  background: #fff3cd;
  color: #856404;
}

.fodmap-high {
  border-left-color: #dc3545;
}

.fodmap-high .fodmap-badge {
  background: #f8d7da;
  color: #721c24;
}

.fodmap-unknown {
  border-left-color: #6c757d;
}

.fodmap-unknown .fodmap-badge {
  background: #e2e3e5;
  color: #495057;
}
