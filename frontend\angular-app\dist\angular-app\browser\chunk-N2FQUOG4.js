import{a as b}from"./chunk-ZJ25V4KC.js";import{A as P,E as R,F as E,G as S,H as F,J as v,M as D,a as y,b as h,c as O,d as k,e as M,f as m,g as f,l as i,m as _,n as w,o as p,r as d,s as a,t as r,v as I,w as s,x as c,y as u,z as g}from"./chunk-73GOI2TA.js";var x=class t{constructor(n){this.http=n}apiBaseUrl=b.apiBaseUrl;getRecipes(){return this.http.get(`${this.apiBaseUrl}/recipes`).pipe(M(n=>{if(n.error||!n.data||n.data.length===0)return[n];let e=n.data.map(o=>this.getRecipeIngredients(o.id).pipe(O(l=>(console.log(`Ingredients for recipe ${o.id}:`,l),h(y({},o),{ingredients:l.error?[]:l.data?.ingredients||[]})))));return k(e).pipe(O(o=>(console.log("Recipes with ingredients:",o),h(y({},n),{data:o}))))}))}getRecipeIngredients(n){return this.http.get(`${this.apiBaseUrl}/recipes/${n}/ingredients`)}getIngredients(){return this.http.get(`${this.apiBaseUrl}/ingredients?limit=100`)}static \u0275fac=function(e){return new(e||t)(f(v))};static \u0275prov=m({token:t,factory:t.\u0275fac,providedIn:"root"})};var C=class t{constructor(n){this.http=n}apiBaseUrl=b.apiBaseUrl;getCategories(){return this.http.get(`${this.apiBaseUrl}/categories`)}static \u0275fac=function(e){return new(e||t)(f(v))};static \u0275prov=m({token:t,factory:t.\u0275fac,providedIn:"root"})};function U(t,n){t&1&&(a(0,"div",10),c(1,"\u{1F373} Loading delicious recipes..."),r())}function B(t,n){if(t&1&&(a(0,"div",11),c(1),r()),t&2){let e=s();i(),u(e.error)}}function H(t,n){t&1&&(a(0,"div",12),c(1," No recipes found. Use the admin panel to add some recipes! "),r())}function q(t,n){if(t&1&&(a(0,"div",26),c(1),r()),t&2){let e=s().$implicit;i(),g(" ",e.description," ")}}function A(t,n){if(t&1&&(a(0,"span",27),c(1),r()),t&2){let e=s().$implicit;i(),g(" \u23F1\uFE0F ",e.preparation_time," min ")}}function K(t,n){if(t&1&&(a(0,"span",27),c(1),r()),t&2){let e=s().$implicit;i(),g(" \u{1F465} Serves ",e.serving_size," ")}}function L(t,n){if(t&1&&(a(0,"div",32)(1,"span",33),c(2),r(),a(3,"div",34)(4,"span",35),c(5),r(),a(6,"span",36),c(7),r()()()),t&2){let e=n.$implicit;d("ngClass","fodmap-"+(e.fodmap_level||"unknown").toLowerCase()),i(2),u(e.name),i(3),P(" ",e.quantity," ",e.quantity_unit||"unit"," "),i(),d("ngClass","fodmap-"+(e.fodmap_level||"unknown").toLowerCase()),i(),g(" ",e.fodmap_level||"UNKNOWN"," ")}}function Y(t,n){if(t&1&&(a(0,"div",28)(1,"div",29),c(2,"\u{1F958} Ingredients"),r(),a(3,"div",30),p(4,L,8,6,"div",31),r()()),t&2){let e=s().$implicit;i(4),d("ngForOf",e.ingredients)}}function G(t,n){t&1&&(a(0,"div",37),c(1," No ingredients listed "),r())}function J(t,n){if(t&1&&(a(0,"div",19)(1,"div",20),c(2),r(),p(3,q,2,1,"div",21),a(4,"div",22),p(5,A,2,1,"span",23)(6,K,2,1,"span",23),r(),p(7,Y,5,1,"div",24)(8,G,2,0,"div",25),r()),t&2){let e=n.$implicit;i(2),u(e.title),i(),d("ngIf",e.description),i(2),d("ngIf",e.preparation_time),i(),d("ngIf",e.serving_size),i(),d("ngIf",e.ingredients&&e.ingredients.length>0),i(),d("ngIf",!e.ingredients||e.ingredients.length===0)}}function V(t,n){if(t&1&&(a(0,"div",15)(1,"h2",16),c(2),r(),a(3,"div",17),p(4,J,9,6,"div",18),r()()),t&2){let e=n.$implicit,o=s(2);i(2),P("",o.getCategoryEmoji(e)," ",e),i(2),d("ngForOf",o.groupedRecipes[e])}}function Q(t,n){if(t&1&&(a(0,"div",13),p(1,V,5,3,"div",14),r()),t&2){let e=s();i(),d("ngForOf",e.getCategoryKeys())}}var j=class t{constructor(n,e,o){this.recipeService=n;this.categoryService=e;this.router=o}recipes=[];categories=[];groupedRecipes={};loading=!0;error="";categoryEmojis={\u015Aniadanie:"\u{1F305}",Obiad:"\u{1F37D}\uFE0F",Kolacja:"\u{1F319}",Przek\u0105ska:"\u{1F36A}"};ngOnInit(){console.log("RecipesComponent ngOnInit called"),this.loadData()}loadData(){this.recipeService.getRecipes().subscribe({next:n=>{console.log("Recipes response:",n),this.recipes=n?.data||[],this.categoryService.getCategories().subscribe({next:e=>{console.log("Categories response:",e),this.categories=e?.data||[],this.groupRecipesByCategory(),this.loading=!1},error:e=>{console.error("Categories error:",e),this.groupRecipesByCategory(),this.loading=!1}})},error:n=>{console.error("Recipes error:",n),this.error="Failed to load recipes: "+n.message,this.loading=!1}})}groupRecipesByCategory(){this.groupedRecipes={},this.recipes.forEach(n=>{let e=n.category_name||"Uncategorized";this.groupedRecipes[e]||(this.groupedRecipes[e]=[]),this.groupedRecipes[e].push(n)})}getCategoryKeys(){return Object.keys(this.groupedRecipes)}getCategoryEmoji(n){return this.categoryEmojis[n]||"\u{1F374}"}goToIngredients(){this.router.navigate(["/ingredients"])}openAdminPanel(){window.open("admin.html","_blank")}static \u0275fac=function(e){return new(e||t)(_(x),_(C),_(D))};static \u0275cmp=w({type:t,selectors:[["app-recipes"]],decls:13,vars:4,consts:[[1,"recipes-container"],[1,"header-content"],[1,"logo"],[1,"nav-buttons"],[1,"nav-button",3,"click"],[1,"nav-button","admin-button",3,"click"],["class","loading",4,"ngIf"],["class","error",4,"ngIf"],["class","no-recipes",4,"ngIf"],["class","content",4,"ngIf"],[1,"loading"],[1,"error"],[1,"no-recipes"],[1,"content"],["class","category-section",4,"ngFor","ngForOf"],[1,"category-section"],[1,"category-title"],[1,"recipes-grid"],["class","recipe-card",4,"ngFor","ngForOf"],[1,"recipe-card"],[1,"recipe-title"],["class","recipe-description",4,"ngIf"],[1,"recipe-meta"],["class","meta-item",4,"ngIf"],["class","ingredients-section",4,"ngIf"],["class","no-ingredients",4,"ngIf"],[1,"recipe-description"],[1,"meta-item"],[1,"ingredients-section"],[1,"ingredients-title"],[1,"ingredients-list"],["class","ingredient-item",3,"ngClass",4,"ngFor","ngForOf"],[1,"ingredient-item",3,"ngClass"],[1,"ingredient-name"],[1,"ingredient-details"],[1,"ingredient-quantity"],[1,"fodmap-badge",3,"ngClass"],[1,"no-ingredients"]],template:function(e,o){e&1&&(a(0,"div",0)(1,"div",1)(2,"h1",2),c(3,"\u{1F37D}\uFE0F FODMAP Recipes"),r(),a(4,"div",3)(5,"button",4),I("click",function(){return o.goToIngredients()}),c(6,"\u{1F955} Ingredients"),r(),a(7,"button",5),I("click",function(){return o.openAdminPanel()}),c(8,"\u2699\uFE0F Admin Panel"),r()()(),p(9,U,2,0,"div",6)(10,B,2,1,"div",7)(11,H,2,0,"div",8)(12,Q,2,1,"div",9),r()),e&2&&(i(9),d("ngIf",o.loading),i(),d("ngIf",o.error),i(),d("ngIf",!o.loading&&!o.error&&o.recipes.length===0),i(),d("ngIf",!o.loading&&!o.error&&o.recipes.length>0))},dependencies:[F,R,E,S],styles:[".recipes-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:2rem;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;margin-bottom:2rem;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);padding:1rem 2rem;border-radius:20px;box-shadow:0 10px 30px #0000001a}.logo[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;background:linear-gradient(45deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;margin:0}.nav-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.nav-button[_ngcontent-%COMP%]{padding:.75rem 1.5rem;border:none;border-radius:25px;cursor:pointer;font-weight:600;text-decoration:none;display:inline-block;transition:all .3s ease;font-size:.9rem;background:linear-gradient(45deg,#667eea,#764ba2);color:#fff}.nav-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 5px 15px #667eea66}.admin-button[_ngcontent-%COMP%]{background:linear-gradient(45deg,#28a745,#20c997)!important}.admin-button[_ngcontent-%COMP%]:hover{box-shadow:0 5px 15px #28a74566!important}.loading[_ngcontent-%COMP%], .error[_ngcontent-%COMP%], .no-recipes[_ngcontent-%COMP%]{text-align:center;padding:2rem;font-size:1.2rem;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px;box-shadow:0 10px 30px #0000001a}.error[_ngcontent-%COMP%]{color:#dc3545;background:#f8d7da;border:1px solid #f5c6cb}.content[_ngcontent-%COMP%]{margin-top:2rem}.category-section[_ngcontent-%COMP%]{margin:2rem 0;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);padding:2rem;border-radius:20px;box-shadow:0 10px 30px #0000001a}.category-title[_ngcontent-%COMP%]{font-size:1.5rem;color:#495057;margin-bottom:1rem;padding:.5rem 1rem;background:linear-gradient(45deg,#f8f9fa,#e9ecef);border-radius:10px;border-left:4px solid #667eea}.recipes-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:1.5rem;margin-top:1rem}.recipe-card[_ngcontent-%COMP%]{background:#fff;border-radius:15px;padding:1.5rem;box-shadow:0 5px 15px #00000014;transition:all .3s ease;border:1px solid #e9ecef}.recipe-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 15px 35px #00000026}.recipe-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;color:#2c3e50;margin-bottom:.5rem}.recipe-description[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:1rem;line-height:1.5}.recipe-meta[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem;flex-wrap:wrap}.meta-item[_ngcontent-%COMP%]{background:#f8f9fa;padding:.25rem .75rem;border-radius:15px;font-size:.85rem;color:#495057}.ingredients-section[_ngcontent-%COMP%]{margin-top:1rem}.ingredients-title[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#495057;margin-bottom:.75rem}.ingredients-list[_ngcontent-%COMP%]{display:grid;gap:.5rem}.ingredient-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.5rem .75rem;background:#f8f9fa;border-radius:8px;border-left:3px solid transparent}.ingredient-name[_ngcontent-%COMP%]{font-weight:500;color:#2c3e50}.ingredient-details[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.ingredient-quantity[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.fodmap-badge[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:700;text-transform:uppercase}.fodmap-low[_ngcontent-%COMP%]{border-left-color:#28a745}.fodmap-low[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.fodmap-moderate[_ngcontent-%COMP%]{border-left-color:#ffc107}.fodmap-moderate[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.fodmap-high[_ngcontent-%COMP%]{border-left-color:#dc3545}.fodmap-high[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.fodmap-unknown[_ngcontent-%COMP%]{border-left-color:#6c757d}.fodmap-unknown[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#e2e3e5;color:#495057}.no-ingredients[_ngcontent-%COMP%]{color:#6c757d;font-style:italic;text-align:center;padding:1rem}"]})};export{j as RecipesComponent};
