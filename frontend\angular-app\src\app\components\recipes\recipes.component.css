.recipes-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.logo {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.nav-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.admin-button {
  background: linear-gradient(45deg, #28a745, #20c997) !important;
}

.admin-button:hover {
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4) !important;
}

.loading, .error, .no-recipes {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.content {
  margin-top: 2rem;
}

.category-section {
  margin: 2rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.category-title {
  font-size: 1.5rem;
  color: #495057;
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.recipe-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.recipe-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.recipe-description {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.recipe-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  color: #495057;
}

.ingredients-section {
  margin-top: 1rem;
}

.ingredients-title {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
}

.ingredients-list {
  display: grid;
  gap: 0.5rem;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.ingredient-name {
  font-weight: 500;
  color: #2c3e50;
}

.ingredient-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ingredient-quantity {
  color: #6c757d;
  font-size: 0.9rem;
}

.fodmap-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
}

.fodmap-low {
  border-left-color: #28a745;
}

.fodmap-low .fodmap-badge {
  background: #d4edda;
  color: #155724;
}

.fodmap-moderate {
  border-left-color: #ffc107;
}

.fodmap-moderate .fodmap-badge {
  background: #fff3cd;
  color: #856404;
}

.fodmap-high {
  border-left-color: #dc3545;
}

.fodmap-high .fodmap-badge {
  background: #f8d7da;
  color: #721c24;
}

.fodmap-unknown {
  border-left-color: #6c757d;
}

.fodmap-unknown .fodmap-badge {
  background: #e2e3e5;
  color: #495057;
}

.no-ingredients {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}
