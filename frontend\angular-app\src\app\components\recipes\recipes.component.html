<div class="recipes-container">
  <div class="header-content">
    <h1 class="logo">FODMAP Recipe Collection</h1>
    <div class="nav-buttons">
      <button (click)="goToIngredients()" class="nav-button">View Ingredients</button>
      <button (click)="openAdminPanel()" class="nav-button admin-button">Admin Panel</button>
    </div>
  </div>

  <div *ngIf="loading" class="loading">🍳 Loading delicious recipes...</div>
  <div *ngIf="error" class="error">{{ error }}</div>

  <div *ngIf="!loading && !error && recipes.length === 0" class="no-recipes">
    No recipes found. Use the admin panel to add some recipes!
  </div>

  <div *ngIf="!loading && !error && recipes.length > 0" class="content">
    <div *ngFor="let categoryName of getCategoryKeys()" class="category-section">
      <h2 class="category-title">{{ categoryName }}</h2>
      
      <div class="recipes-grid">
        <div *ngFor="let recipe of groupedRecipes[categoryName]" class="recipe-card">
          <div class="recipe-title">{{ recipe.title }}</div>

          <div *ngIf="recipe.description" class="recipe-description">
            {{ recipe.description }}
          </div>

          <div class="recipe-meta">
            <span *ngIf="recipe.preparation_time" class="meta-item">
              ⏱️ {{ recipe.preparation_time }} min
            </span>
            <span *ngIf="recipe.serving_size" class="meta-item">
              👥 Serves {{ recipe.serving_size }}
            </span>
          </div>

          <div *ngIf="recipe.ingredients && recipe.ingredients.length > 0" class="ingredients-section">
            <div class="ingredients-title">🥘 Ingredients</div>
            <div class="ingredients-list">
              <div *ngFor="let ingredient of recipe.ingredients"
                   class="ingredient-item"
                   [ngClass]="'fodmap-' + (ingredient.fodmap_level || 'unknown').toLowerCase()">
                <span class="ingredient-name">{{ ingredient.name }}</span>
                <div class="ingredient-details">
                  <span class="ingredient-quantity">
                    {{ ingredient.quantity }} {{ ingredient.quantity_unit || 'unit' }}
                  </span>
                  <span class="fodmap-badge"
                        [ngClass]="'fodmap-' + (ingredient.fodmap_level || 'unknown').toLowerCase()">
                    {{ ingredient.fodmap_level || 'UNKNOWN' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="!recipe.ingredients || recipe.ingredients.length === 0" class="no-ingredients">
            No ingredients listed
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
