{"version": 3, "sources": ["../../../../../../node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\nfunction initZone() {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    class ZoneImpl {\n        static __symbol__ = __symbol__;\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = ZoneImpl.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, ZoneImpl, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        _parent;\n        _name;\n        _properties;\n        _zoneDelegate;\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = (zoneSpec && zoneSpec.properties) || {};\n            this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            }\n            const zoneTask = task;\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            const { type, data: { isPeriodic = false, isRefreshable = false } = {} } = task;\n            if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && zoneTask._transitionTo(running, scheduled);\n            const previousTask = _currentTask;\n            _currentTask = zoneTask;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                const state = task.state;\n                if (state !== notScheduled && state !== unknown) {\n                    if (type == eventTask || isPeriodic || (isRefreshable && state === scheduling)) {\n                        reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n                    }\n                    else {\n                        const zoneDelegates = zoneTask._zoneDelegates;\n                        this._updateTaskCount(zoneTask, -1);\n                        reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n                        if (isRefreshable) {\n                            zoneTask._zoneDelegates = zoneDelegates;\n                        }\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = -1;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task),\n    };\n    class _ZoneDelegate {\n        get zone() {\n            return this._zone;\n        }\n        _zone;\n        _taskCounts = {\n            'microTask': 0,\n            'macroTask': 0,\n            'eventTask': 0,\n        };\n        _parentDelegate;\n        _forkDlgt;\n        _forkZS;\n        _forkCurrZone;\n        _interceptDlgt;\n        _interceptZS;\n        _interceptCurrZone;\n        _invokeDlgt;\n        _invokeZS;\n        _invokeCurrZone;\n        _handleErrorDlgt;\n        _handleErrorZS;\n        _handleErrorCurrZone;\n        _scheduleTaskDlgt;\n        _scheduleTaskZS;\n        _scheduleTaskCurrZone;\n        _invokeTaskDlgt;\n        _invokeTaskZS;\n        _invokeTaskCurrZone;\n        _cancelTaskDlgt;\n        _cancelTaskZS;\n        _cancelTaskCurrZone;\n        _hasTaskDlgt;\n        _hasTaskDlgtOwner;\n        _hasTaskZS;\n        _hasTaskCurrZone;\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt =\n                zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = this._zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this._zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS\n                ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec)\n                : new ZoneImpl(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS\n                ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source)\n                : callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS\n                ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source)\n                : callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS\n                ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error)\n                : true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS\n                ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs)\n                : task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = (counts[type] = prev + count);\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type,\n                };\n                this.hasTask(this._zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        type;\n        source;\n        invoke;\n        callback;\n        data;\n        scheduleFn;\n        cancelFn;\n        _zone = null;\n        runCount = 0;\n        _zoneDelegates = null;\n        _state = 'notScheduled';\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount,\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask,\n    };\n    let _currentZoneFrame = { parent: null, zone: new ZoneImpl(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return ZoneImpl;\n}\n\nfunction loadZone() {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    const global = globalThis;\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n        throw new Error('Zone already loaded.');\n    }\n    // Initialize global `Zone` constant.\n    global['Zone'] ??= initZone();\n    return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = (isWindowExists && internalWindow) || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) &&\n    typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]' &&\n    !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result =\n            listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (\n        // https://github.com/angular/angular/issues/47579\n        // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n        // This is the only specific case we should check for. The spec defines that the\n        // `returnValue` attribute represents the message to show the user. When the event\n        // is created, this attribute must be set to the empty string.\n        event.type === 'beforeunload' &&\n            // To prevent any breaking changes resulting from this change, given that\n            // it was already causing a significant number of failures in G3, we have hidden\n            // that behavior behind a global configuration flag. Consumers can enable this\n            // flag explicitly if they want the `beforeunload` event to be handled as defined\n            // in the specification.\n            _global[enableBeforeunloadSymbol] &&\n            // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n            // `typeof result` is a string.\n            typeof result === 'string') {\n            event.returnValue = result;\n        }\n        else if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // In some versions of Windows, the `this` context may be undefined\n        // in on-property callbacks.\n        // To handle this edge case, we check if `this` is falsy and\n        // fallback to `_global` if needed.\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // https://github.com/angular/zone.js/issues/978\n        // If an inline handler (like `onload`) was defined before zone.js was loaded,\n        // call the original descriptor's setter to clean it up.\n        originalDescSet?.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    },\n                });\n            }\n        })(prop);\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) { }\n    return ieOrEdge;\n}\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nfunction isNumber(value) {\n    return typeof value === 'number';\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true,\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n        // eliminating the need to create a new object solely for passing data.\n        // WARNING: This object has a static lifetime, meaning it is not created\n        // each time `addEventListener` is called. It is instantiated only once\n        // and captured by reference inside the `addEventListener` and\n        // `removeEventListener` functions. Do not add any new properties to this\n        // object, as doing so would necessitate maintaining the information\n        // between `addEventListener` calls.\n        const taskData = {};\n        const nativeAddEventListener = (proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER]);\n        const nativeRemoveEventListener = (proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER]);\n        const nativeListeners = (proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER]);\n        const nativeRemoveAllListeners = (proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]);\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        /**\n         * In the context of events and listeners, this function will be\n         * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n         * Cancelling a task is primarily used to remove event listeners from\n         * the task target.\n         */\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (task.removeAbortListener) {\n                                task.removeAbortListener();\n                                task.removeAbortListener = null;\n                            }\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return ((typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate));\n        };\n        const compare = patchOptions?.diff || compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        function copyEventListenerOptions(options) {\n            if (typeof options === 'object' && options !== null) {\n                // We need to destructure the target `options` object since it may\n                // be frozen or sealed (possibly provided implicitly by a third-party\n                // library), or its properties may be readonly.\n                const newOptions = { ...options };\n                // The `signal` option was recently introduced, which caused regressions in\n                // third-party scenarios where `AbortController` was directly provided to\n                // `addEventListener` as options. For instance, in cases like\n                // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n                // which is valid because `AbortController` includes a `signal` getter, spreading\n                // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n                // isn't feasible since `AbortController` is a built-in object type, and attempting\n                // to create a new object directly with it as the prototype might result in\n                // unexpected behavior.\n                if (options.signal) {\n                    newOptions.signal = options.signal;\n                }\n                return newOptions;\n            }\n            return options;\n        }\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // To improve `addEventListener` performance, we will create the callback\n                // for the task later when the task is invoked.\n                let isEventListenerObject = false;\n                if (typeof delegate !== 'function') {\n                    // This checks whether the provided listener argument is an object with\n                    // a `handleEvent` method (since we can call `addEventListener` with a\n                    // function `event => ...` or with an object `{ handleEvent: event => ... }`).\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isEventListenerObject = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n                const signal = options?.signal;\n                if (signal?.aborted) {\n                    // the signal is an aborted one, just return without attaching the event listener.\n                    return;\n                }\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source =\n                        constructorName +\n                            addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // In the code below, `options` should no longer be reassigned; instead, it\n                // should only be mutated. This is because we pass that object to the native\n                // `addEventListener`.\n                // It's generally recommended to use the same object reference for options.\n                // This ensures consistency and avoids potential issues.\n                taskData.options = options;\n                if (once) {\n                    // When using `addEventListener` with the `once` option, we don't pass\n                    // the `once` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `once` setting and handle it ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                if (signal) {\n                    // When using `addEventListener` with the `signal` option, we don't pass\n                    // the `signal` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `signal` setting and handle it ourselves.\n                    taskData.options.signal = undefined;\n                }\n                // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n                // which in turn calls the native `addEventListener`. This is why `taskData.options`\n                // is updated before scheduling the task, as `customScheduleGlobal` uses\n                // `taskData.options` to pass it to the native `addEventListener`.\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                if (signal) {\n                    // after task is scheduled, we need to store the signal back to task.options\n                    taskData.options.signal = signal;\n                    // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n                    // primarily used for preventing strong references cycles. `onAbort` is always reachable\n                    // as it's an event listener, so its closure retains a strong reference to the `task`.\n                    const onAbort = () => task.zone.cancelTask(task);\n                    nativeListener.call(signal, 'abort', onAbort, { once: true });\n                    // We need to remove the `abort` listener when the event listener is going to be removed,\n                    // as it creates a closure that captures `task`. This closure retains a reference to the\n                    // `task` object even after it goes out of scope, preventing `task` from being garbage\n                    // collected.\n                    task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n                }\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    taskData.options.once = true;\n                }\n                if (typeof task.options !== 'boolean') {\n                    // We should save the options on the task (if it's an object) because\n                    // we'll be using `task.options` later when removing the event listener\n                    // and passing it back to `removeEventListener`.\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isEventListenerObject) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            // `existingTasks` may not exist if the `addEventListener` was called before\n            // it was patched by zone.js. Please refer to the attached issue for\n            // clarification, particularly after the `if` condition, before calling\n            // the native `removeEventListener`.\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates with capture=false were removed\n                            // https:// github.com/angular/angular/issues/31643\n                            // https://github.com/angular/angular/issues/54581\n                            if (!capture && typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        // In all other conditions, when `addEventListener` is called after being\n                        // patched by zone.js, we would always find an event task on the `EventTarget`.\n                        // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n                        // which ultimately removes an event listener and cleans up the abort listener\n                        // (if an `AbortSignal` was provided when scheduling a task).\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // https://github.com/angular/zone.js/issues/930\n            // We may encounter a situation where the `addEventListener` was\n            // called on the event target before zone.js is loaded, resulting\n            // in no task being stored on the event target due to its invocation\n            // of the native implementation. In this scenario, we simply need to\n            // invoke the native `removeEventListener`.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks\n            ? captureFalseTasks.concat(captureTrueTasks)\n            : captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        const handleOrId = setNative.apply(window, data.args);\n        // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n        // to this so that we do not cause potentally leaks when using `setTimeout`\n        // since this can be periodic when using `.refresh`.\n        if (isNumber(handleOrId)) {\n            data.handleId = handleOrId;\n        }\n        else {\n            data.handle = handleOrId;\n            // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n            data.isRefreshable = isFunction(handleOrId.refresh);\n        }\n        return task;\n    }\n    function clearTask(task) {\n        const { handle, handleId } = task.data;\n        return clearNative.call(window, handle ?? handleId);\n    }\n    setNative = patchMethod(window, setName, (delegate) => function (self, args) {\n        if (isFunction(args[0])) {\n            const options = {\n                isRefreshable: false,\n                isPeriodic: nameSuffix === 'Interval',\n                delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n                args: args,\n            };\n            const callback = args[0];\n            args[0] = function timer() {\n                try {\n                    return callback.apply(this, arguments);\n                }\n                finally {\n                    // issue-934, task will be cancelled\n                    // even it is a periodic task such as\n                    // setInterval\n                    // https://github.com/angular/angular/issues/40387\n                    // Cleanup tasksByHandleId should be handled before scheduleTask\n                    // Since some zoneSpec may intercept and doesn't trigger\n                    // scheduleFn(scheduleTask) provided here.\n                    const { handle, handleId, isPeriodic, isRefreshable } = options;\n                    if (!isPeriodic && !isRefreshable) {\n                        if (handleId) {\n                            // in non-nodejs env, we remove timerId\n                            // from local cache\n                            delete tasksByHandleId[handleId];\n                        }\n                        else if (handle) {\n                            // Node returns complex objects as handleIds\n                            // we remove task reference from timer object\n                            handle[taskSymbol] = null;\n                        }\n                    }\n                }\n            };\n            const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n            if (!task) {\n                return task;\n            }\n            // Node.js must additionally support the ref and unref functions.\n            const { handleId, handle, isRefreshable, isPeriodic } = task.data;\n            if (handleId) {\n                // for non nodejs env, we save handleId: task\n                // mapping in local cache for clearTimeout\n                tasksByHandleId[handleId] = task;\n            }\n            else if (handle) {\n                // for nodejs env, we save task\n                // reference in timerId Object for clearTimeout\n                handle[taskSymbol] = task;\n                if (isRefreshable && !isPeriodic) {\n                    const originalRefresh = handle.refresh;\n                    handle.refresh = function () {\n                        const { zone, state } = task;\n                        if (state === 'notScheduled') {\n                            task._state = 'scheduled';\n                            zone._updateTaskCount(task, 1);\n                        }\n                        else if (state === 'running') {\n                            task._state = 'scheduling';\n                        }\n                        return originalRefresh.call(this);\n                    };\n                }\n            }\n            return handle ?? handleId ?? task;\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(window, args);\n        }\n    });\n    clearNative = patchMethod(window, cancelName, (delegate) => function (self, args) {\n        const id = args[0];\n        let task;\n        if (isNumber(id)) {\n            // non nodejs env.\n            task = tasksByHandleId[id];\n            delete tasksByHandleId[id];\n        }\n        else {\n            // nodejs env ?? other environments.\n            task = id?.[taskSymbol];\n            if (task) {\n                id[taskSymbol] = null;\n            }\n            else {\n                task = id;\n            }\n        }\n        if (task?.type) {\n            if (task.cancelFn) {\n                // Do not cancel already canceled functions\n                task.zone.cancelTask(task);\n            }\n        }\n        else {\n            // cause an error by calling it directly.\n            delegate.apply(window, args);\n        }\n    });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n    const callbacks = [\n        'connectedCallback',\n        'disconnectedCallback',\n        'adoptedCallback',\n        'attributeChangedCallback',\n        'formAssociatedCallback',\n        'formDisabledCallback',\n        'formResetCallback',\n        'formStateRestoreCallback',\n    ];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter((ip) => ip.target === target);\n    if (tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter((op) => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter((name) => name.startsWith('on') && name.length > 2)\n        .map((name) => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document',\n            'SVGElement',\n            'Element',\n            'HTMLElement',\n            'HTMLBodyElement',\n            'HTMLMediaElement',\n            'HTMLFrameSetElement',\n            'HTMLFrameElement',\n            'HTMLIFrameElement',\n            'HTMLMarqueeElement',\n            'Worker',\n        ]);\n        const ignoreErrorProperties = [];\n        // In older browsers like IE or Edge, event handler properties (e.g., `onclick`)\n        // may not be defined directly on the `window` object but on its prototype (`WindowPrototype`).\n        // To ensure complete coverage, we use the prototype when checking\n        // for and patching these properties.\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest',\n        'XMLHttpRequestEventTarget',\n        'IDBIndex',\n        'IDBRequest',\n        'IDBOpenDBRequest',\n        'IDBDatabase',\n        'IDBTransaction',\n        'IDBCursor',\n        'WebSocket',\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target?.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n    Zone.__load_patch('legacy', (global) => {\n        const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('timers', (global) => {\n        const set = 'set';\n        const clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', (global) => {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', (global, Zone) => {\n        const blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (let i = 0; i < blockingMethods.length; i++) {\n            const name = blockingMethods[i];\n            patchMethod(global, name, (delegate, symbol, name) => {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', (global, Zone, api) => {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', (global, Zone, api) => {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', (global, Zone, api) => {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', (global, Zone, api) => {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', (global, Zone) => {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        const XHR_TASK = zoneSymbol('xhrTask');\n        const XHR_SYNC = zoneSymbol('xhrSync');\n        const XHR_LISTENER = zoneSymbol('xhrListener');\n        const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n        const XHR_URL = zoneSymbol('xhrURL');\n        const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            const XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget) {\n                    const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            const READY_STATE_CHANGE = 'readystatechange';\n            const SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                const data = task.data;\n                const target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                const listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                const newListener = (target[XHR_LISTENER] = () => {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            const loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                const oriInvoke = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (let i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                });\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                const storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                const data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            });\n            const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n            const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n            const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    const options = {\n                        target: self,\n                        url: self[XHR_URL],\n                        isPeriodic: false,\n                        args: args,\n                        aborted: false,\n                    };\n                    const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self &&\n                        self[XHR_ERROR_BEFORE_SCHEDULED] === true &&\n                        !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            });\n            const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n                const task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            });\n        }\n    });\n    Zone.__load_patch('geolocation', (global) => {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                const eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach((eventTask) => {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        const evt = new PromiseRejectionEvent(evtName, {\n                            promise: e.promise,\n                            reason: e.rejection,\n                        });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n    Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n        patchQueueMicrotask(global, api);\n    });\n}\n\nfunction patchPromise(Zone) {\n    Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n        const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        const ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                const className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        const __symbol__ = api.symbol;\n        const _uncaughtPromiseErrors = [];\n        const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n        const symbolPromise = __symbol__('Promise');\n        const symbolThen = __symbol__('then');\n        const creationTrace = '__creationTrace__';\n        api.onUnhandledError = (e) => {\n            if (api.showUncaughtError()) {\n                const rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = () => {\n            while (_uncaughtPromiseErrors.length) {\n                const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(() => {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            }\n        };\n        const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) { }\n        }\n        function isThenable(value) {\n            return value && typeof value.then === 'function';\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        const symbolState = __symbol__('state');\n        const symbolValue = __symbol__('value');\n        const symbolFinally = __symbol__('finally');\n        const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        const symbolParentPromiseState = __symbol__('parentPromiseState');\n        const source = 'Promise.then';\n        const UNRESOLVED = null;\n        const RESOLVED = true;\n        const REJECTED = false;\n        const REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return (v) => {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        const once = function () {\n            let wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        const TYPE_ERROR = 'Promise resolved with itself';\n        const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            const onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                let then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED &&\n                    value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) &&\n                    value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(() => {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    const queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        const trace = Zone.currentTask &&\n                            Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                                configurable: true,\n                                enumerable: false,\n                                writable: true,\n                                value: trace,\n                            });\n                        }\n                    }\n                    for (let i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        let uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' +\n                                readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    const handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) { }\n                promise[symbolState] = REJECTED;\n                for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            const promiseState = promise[symbolState];\n            const delegate = promiseState\n                ? typeof onFulfilled === 'function'\n                    ? onFulfilled\n                    : forwardResolution\n                : typeof onRejected === 'function'\n                    ? onRejected\n                    : forwardRejection;\n            zone.scheduleMicroTask(source, () => {\n                try {\n                    const parentPromiseValue = promise[symbolValue];\n                    const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution\n                        ? []\n                        : [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        const noop = function () { };\n        const AggregateError = global.AggregateError;\n        class ZoneAwarePromise {\n            static toString() {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            }\n            static resolve(value) {\n                if (value instanceof ZoneAwarePromise) {\n                    return value;\n                }\n                return resolvePromise(new this(null), RESOLVED, value);\n            }\n            static reject(error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            }\n            static withResolvers() {\n                const result = {};\n                result.promise = new ZoneAwarePromise((res, rej) => {\n                    result.resolve = res;\n                    result.reject = rej;\n                });\n                return result;\n            }\n            static any(values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                const promises = [];\n                let count = 0;\n                try {\n                    for (let v of values) {\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                let finished = false;\n                const errors = [];\n                return new ZoneAwarePromise((resolve, reject) => {\n                    for (let i = 0; i < promises.length; i++) {\n                        promises[i].then((v) => {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, (err) => {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            }\n            static race(values) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            }\n            static all(values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            }\n            static allSettled(values) {\n                const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: (value) => ({ status: 'fulfilled', value }),\n                    errorCallback: (err) => ({ status: 'rejected', reason: err }),\n                });\n            }\n            static allWithCallback(values, callback) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                let unresolvedCount = 2;\n                let valueIndex = 0;\n                const resolvedValues = [];\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    const curValueIndex = valueIndex;\n                    try {\n                        value.then((value) => {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, (err) => {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            }\n            constructor(executor) {\n                const promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    const onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            get [Symbol.toStringTag]() {\n                return 'Promise';\n            }\n            get [Symbol.species]() {\n                return ZoneAwarePromise;\n            }\n            then(onFulfilled, onRejected) {\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and\n                // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n                // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            }\n            catch(onRejected) {\n                return this.then(null, onRejected);\n            }\n            finally(onFinally) {\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            }\n        }\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        const NativePromise = (global[symbolPromise] = global['Promise']);\n        global['Promise'] = ZoneAwarePromise;\n        const symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            const proto = Ctor.prototype;\n            const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            const originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                    originalThen.call(this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                let resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                let ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', (delegate) => zoneify(delegate));\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n}\n\nfunction patchToString(Zone) {\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', (global) => {\n        // patch Func.prototype.toString to let them look like native\n        const originalFunctionToString = Function.prototype.toString;\n        const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n        const PROMISE_SYMBOL = zoneSymbol('Promise');\n        const ERROR_SYMBOL = zoneSymbol('Error');\n        const newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    const nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    const nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        const originalObjectToString = Object.prototype.toString;\n        const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = (target[symbol] = target[method]);\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n    Zone.__load_patch('util', (global, Zone, api) => {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        const eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n        // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n        // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n        // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n        // supported for backwards compatibility.\n        const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = () => ({\n            globalSources,\n            zoneSymbolEventNames,\n            eventNames,\n            isBrowser,\n            isMix,\n            isNode,\n            TRUE_STR,\n            FALSE_STR,\n            ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR,\n        });\n    });\n}\n\nfunction patchCommon(Zone) {\n    patchPromise(Zone);\n    patchToString(Zone);\n    patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);\n"], "mappings": ";;;;;;AAMA,IAAM,SAAS;AAGf,SAAS,WAAW,MAAM;AACtB,QAAM,eAAe,OAAO,sBAAsB,KAAK;AACvD,SAAO,eAAe;AAC1B;AACA,SAAS,WAAW;AAChB,QAAM,cAAc,OAAO,aAAa;AACxC,WAAS,KAAK,MAAM;AAChB,mBAAe,YAAY,MAAM,KAAK,YAAY,MAAM,EAAE,IAAI;AAAA,EAClE;AACA,WAAS,mBAAmB,MAAM,OAAO;AACrC,mBAAe,YAAY,SAAS,KAAK,YAAY,SAAS,EAAE,MAAM,KAAK;AAAA,EAC/E;AACA,OAAK,MAAM;AAAA,EACX,MAAM,SAAS;AAAA,IACX,OAAO,aAAa;AAAA,IACpB,OAAO,oBAAoB;AACvB,UAAI,OAAO,SAAS,MAAM,QAAQ,kBAAkB,GAAG;AACnD,cAAM,IAAI,MAAM,+RAI0C;AAAA,MAC9D;AAAA,IACJ;AAAA,IACA,WAAW,OAAO;AACd,UAAI,OAAO,SAAS;AACpB,aAAO,KAAK,QAAQ;AAChB,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,UAAU;AACjB,aAAO,kBAAkB;AAAA,IAC7B;AAAA,IACA,WAAW,cAAc;AACrB,aAAO;AAAA,IACX;AAAA,IACA,OAAO,aAAa,MAAM,IAAI,kBAAkB,OAAO;AACnD,UAAI,QAAQ,eAAe,IAAI,GAAG;AAI9B,cAAM,iBAAiB,OAAO,WAAW,yBAAyB,CAAC,MAAM;AACzE,YAAI,CAAC,mBAAmB,gBAAgB;AACpC,gBAAM,MAAM,2BAA2B,IAAI;AAAA,QAC/C;AAAA,MACJ,WACS,CAAC,OAAO,oBAAoB,IAAI,GAAG;AACxC,cAAM,WAAW,UAAU;AAC3B,aAAK,QAAQ;AACb,gBAAQ,IAAI,IAAI,GAAG,QAAQ,UAAU,IAAI;AACzC,2BAAmB,UAAU,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,IAAI,SAAS;AACT,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,OAAO;AACP,aAAO,KAAK;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,QAAQ,UAAU;AAC1B,WAAK,UAAU;AACf,WAAK,QAAQ,WAAW,SAAS,QAAQ,YAAY;AACrD,WAAK,cAAe,YAAY,SAAS,cAAe,CAAC;AACzD,WAAK,gBAAgB,IAAI,cAAc,MAAM,KAAK,WAAW,KAAK,QAAQ,eAAe,QAAQ;AAAA,IACrG;AAAA,IACA,IAAI,KAAK;AACL,YAAM,OAAO,KAAK,YAAY,GAAG;AACjC,UAAI;AACA,eAAO,KAAK,YAAY,GAAG;AAAA,IACnC;AAAA,IACA,YAAY,KAAK;AACb,UAAI,UAAU;AACd,aAAO,SAAS;AACZ,YAAI,QAAQ,YAAY,eAAe,GAAG,GAAG;AACzC,iBAAO;AAAA,QACX;AACA,kBAAU,QAAQ;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AAAA,IACA,KAAK,UAAU;AACX,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,oBAAoB;AACxC,aAAO,KAAK,cAAc,KAAK,MAAM,QAAQ;AAAA,IACjD;AAAA,IACA,KAAK,UAAU,QAAQ;AACnB,UAAI,OAAO,aAAa,YAAY;AAChC,cAAM,IAAI,MAAM,6BAA6B,QAAQ;AAAA,MACzD;AACA,YAAM,YAAY,KAAK,cAAc,UAAU,MAAM,UAAU,MAAM;AACrE,YAAM,OAAO;AACb,aAAO,WAAY;AACf,eAAO,KAAK,WAAW,WAAW,MAAM,WAAW,MAAM;AAAA,MAC7D;AAAA,IACJ;AAAA,IACA,IAAI,UAAU,WAAW,WAAW,QAAQ;AACxC,0BAAoB,EAAE,QAAQ,mBAAmB,MAAM,KAAK;AAC5D,UAAI;AACA,eAAO,KAAK,cAAc,OAAO,MAAM,UAAU,WAAW,WAAW,MAAM;AAAA,MACjF,UACA;AACI,4BAAoB,kBAAkB;AAAA,MAC1C;AAAA,IACJ;AAAA,IACA,WAAW,UAAU,YAAY,MAAM,WAAW,QAAQ;AACtD,0BAAoB,EAAE,QAAQ,mBAAmB,MAAM,KAAK;AAC5D,UAAI;AACA,YAAI;AACA,iBAAO,KAAK,cAAc,OAAO,MAAM,UAAU,WAAW,WAAW,MAAM;AAAA,QACjF,SACO,OAAO;AACV,cAAI,KAAK,cAAc,YAAY,MAAM,KAAK,GAAG;AAC7C,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ,UACA;AACI,4BAAoB,kBAAkB;AAAA,MAC1C;AAAA,IACJ;AAAA,IACA,QAAQ,MAAM,WAAW,WAAW;AAChC,UAAI,KAAK,QAAQ,MAAM;AACnB,cAAM,IAAI,MAAM,iEACX,KAAK,QAAQ,SAAS,OACvB,kBACA,KAAK,OACL,GAAG;AAAA,MACX;AACA,YAAM,WAAW;AAIjB,YAAM,EAAE,MAAM,MAAM,EAAE,aAAa,OAAO,gBAAgB,MAAM,IAAI,CAAC,EAAE,IAAI;AAC3E,UAAI,KAAK,UAAU,iBAAiB,SAAS,aAAa,SAAS,YAAY;AAC3E;AAAA,MACJ;AACA,YAAM,eAAe,KAAK,SAAS;AACnC,sBAAgB,SAAS,cAAc,SAAS,SAAS;AACzD,YAAM,eAAe;AACrB,qBAAe;AACf,0BAAoB,EAAE,QAAQ,mBAAmB,MAAM,KAAK;AAC5D,UAAI;AACA,YAAI,QAAQ,aAAa,KAAK,QAAQ,CAAC,cAAc,CAAC,eAAe;AACjE,eAAK,WAAW;AAAA,QACpB;AACA,YAAI;AACA,iBAAO,KAAK,cAAc,WAAW,MAAM,UAAU,WAAW,SAAS;AAAA,QAC7E,SACO,OAAO;AACV,cAAI,KAAK,cAAc,YAAY,MAAM,KAAK,GAAG;AAC7C,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ,UACA;AAGI,cAAM,QAAQ,KAAK;AACnB,YAAI,UAAU,gBAAgB,UAAU,SAAS;AAC7C,cAAI,QAAQ,aAAa,cAAe,iBAAiB,UAAU,YAAa;AAC5E,4BAAgB,SAAS,cAAc,WAAW,SAAS,UAAU;AAAA,UACzE,OACK;AACD,kBAAM,gBAAgB,SAAS;AAC/B,iBAAK,iBAAiB,UAAU,EAAE;AAClC,4BAAgB,SAAS,cAAc,cAAc,SAAS,YAAY;AAC1E,gBAAI,eAAe;AACf,uBAAS,iBAAiB;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ;AACA,4BAAoB,kBAAkB;AACtC,uBAAe;AAAA,MACnB;AAAA,IACJ;AAAA,IACA,aAAa,MAAM;AACf,UAAI,KAAK,QAAQ,KAAK,SAAS,MAAM;AAGjC,YAAI,UAAU;AACd,eAAO,SAAS;AACZ,cAAI,YAAY,KAAK,MAAM;AACvB,kBAAM,MAAM,8BAA8B,KAAK,IAAI,8CAA8C,KAAK,KAAK,IAAI,EAAE;AAAA,UACrH;AACA,oBAAU,QAAQ;AAAA,QACtB;AAAA,MACJ;AACA,WAAK,cAAc,YAAY,YAAY;AAC3C,YAAM,gBAAgB,CAAC;AACvB,WAAK,iBAAiB;AACtB,WAAK,QAAQ;AACb,UAAI;AACA,eAAO,KAAK,cAAc,aAAa,MAAM,IAAI;AAAA,MACrD,SACO,KAAK;AAGR,aAAK,cAAc,SAAS,YAAY,YAAY;AAEpD,aAAK,cAAc,YAAY,MAAM,GAAG;AACxC,cAAM;AAAA,MACV;AACA,UAAI,KAAK,mBAAmB,eAAe;AAEvC,aAAK,iBAAiB,MAAM,CAAC;AAAA,MACjC;AACA,UAAI,KAAK,SAAS,YAAY;AAC1B,aAAK,cAAc,WAAW,UAAU;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB;AACtD,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,MAAS,CAAC;AAAA,IACvG;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AACpE,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,YAAY,CAAC;AAAA,IAC1G;AAAA,IACA,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AACpE,aAAO,KAAK,aAAa,IAAI,SAAS,WAAW,QAAQ,UAAU,MAAM,gBAAgB,YAAY,CAAC;AAAA,IAC1G;AAAA,IACA,WAAW,MAAM;AACb,UAAI,KAAK,QAAQ;AACb,cAAM,IAAI,MAAM,uEACX,KAAK,QAAQ,SAAS,OACvB,kBACA,KAAK,OACL,GAAG;AACX,UAAI,KAAK,UAAU,aAAa,KAAK,UAAU,SAAS;AACpD;AAAA,MACJ;AACA,WAAK,cAAc,WAAW,WAAW,OAAO;AAChD,UAAI;AACA,aAAK,cAAc,WAAW,MAAM,IAAI;AAAA,MAC5C,SACO,KAAK;AAER,aAAK,cAAc,SAAS,SAAS;AACrC,aAAK,cAAc,YAAY,MAAM,GAAG;AACxC,cAAM;AAAA,MACV;AACA,WAAK,iBAAiB,MAAM,EAAE;AAC9B,WAAK,cAAc,cAAc,SAAS;AAC1C,WAAK,WAAW;AAChB,aAAO;AAAA,IACX;AAAA,IACA,iBAAiB,MAAM,OAAO;AAC1B,YAAM,gBAAgB,KAAK;AAC3B,UAAI,SAAS,IAAI;AACb,aAAK,iBAAiB;AAAA,MAC1B;AACA,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,sBAAc,CAAC,EAAE,iBAAiB,KAAK,MAAM,KAAK;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,cAAc;AAAA,IAChB,MAAM;AAAA,IACN,WAAW,CAAC,UAAU,GAAG,QAAQ,iBAAiB,SAAS,QAAQ,QAAQ,YAAY;AAAA,IACvF,gBAAgB,CAAC,UAAU,GAAG,QAAQ,SAAS,SAAS,aAAa,QAAQ,IAAI;AAAA,IACjF,cAAc,CAAC,UAAU,GAAG,QAAQ,MAAM,WAAW,cAAc,SAAS,WAAW,QAAQ,MAAM,WAAW,SAAS;AAAA,IACzH,cAAc,CAAC,UAAU,GAAG,QAAQ,SAAS,SAAS,WAAW,QAAQ,IAAI;AAAA,EACjF;AAAA,EACA,MAAM,cAAc;AAAA,IAChB,IAAI,OAAO;AACP,aAAO,KAAK;AAAA,IAChB;AAAA,IACA;AAAA,IACA,cAAc;AAAA,MACV,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,MAAM,gBAAgB,UAAU;AACxC,WAAK,QAAQ;AACb,WAAK,kBAAkB;AACvB,WAAK,UAAU,aAAa,YAAY,SAAS,SAAS,WAAW,eAAe;AACpF,WAAK,YAAY,aAAa,SAAS,SAAS,iBAAiB,eAAe;AAChF,WAAK,gBACD,aAAa,SAAS,SAAS,KAAK,QAAQ,eAAe;AAC/D,WAAK,eACD,aAAa,SAAS,cAAc,WAAW,eAAe;AAClE,WAAK,iBACD,aAAa,SAAS,cAAc,iBAAiB,eAAe;AACxE,WAAK,qBACD,aAAa,SAAS,cAAc,KAAK,QAAQ,eAAe;AACpE,WAAK,YAAY,aAAa,SAAS,WAAW,WAAW,eAAe;AAC5E,WAAK,cACD,aAAa,SAAS,WAAW,iBAAiB,eAAe;AACrE,WAAK,kBACD,aAAa,SAAS,WAAW,KAAK,QAAQ,eAAe;AACjE,WAAK,iBACD,aAAa,SAAS,gBAAgB,WAAW,eAAe;AACpE,WAAK,mBACD,aAAa,SAAS,gBAAgB,iBAAiB,eAAe;AAC1E,WAAK,uBACD,aAAa,SAAS,gBAAgB,KAAK,QAAQ,eAAe;AACtE,WAAK,kBACD,aAAa,SAAS,iBAAiB,WAAW,eAAe;AACrE,WAAK,oBACD,aAAa,SAAS,iBAAiB,iBAAiB,eAAe;AAC3E,WAAK,wBACD,aAAa,SAAS,iBAAiB,KAAK,QAAQ,eAAe;AACvE,WAAK,gBACD,aAAa,SAAS,eAAe,WAAW,eAAe;AACnE,WAAK,kBACD,aAAa,SAAS,eAAe,iBAAiB,eAAe;AACzE,WAAK,sBACD,aAAa,SAAS,eAAe,KAAK,QAAQ,eAAe;AACrE,WAAK,gBACD,aAAa,SAAS,eAAe,WAAW,eAAe;AACnE,WAAK,kBACD,aAAa,SAAS,eAAe,iBAAiB,eAAe;AACzE,WAAK,sBACD,aAAa,SAAS,eAAe,KAAK,QAAQ,eAAe;AACrE,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AACxB,YAAM,kBAAkB,YAAY,SAAS;AAC7C,YAAM,gBAAgB,kBAAkB,eAAe;AACvD,UAAI,mBAAmB,eAAe;AAGlC,aAAK,aAAa,kBAAkB,WAAW;AAC/C,aAAK,eAAe;AACpB,aAAK,oBAAoB;AACzB,aAAK,mBAAmB,KAAK;AAC7B,YAAI,CAAC,SAAS,gBAAgB;AAC1B,eAAK,kBAAkB;AACvB,eAAK,oBAAoB;AACzB,eAAK,wBAAwB,KAAK;AAAA,QACtC;AACA,YAAI,CAAC,SAAS,cAAc;AACxB,eAAK,gBAAgB;AACrB,eAAK,kBAAkB;AACvB,eAAK,sBAAsB,KAAK;AAAA,QACpC;AACA,YAAI,CAAC,SAAS,cAAc;AACxB,eAAK,gBAAgB;AACrB,eAAK,kBAAkB;AACvB,eAAK,sBAAsB,KAAK;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,KAAK,YAAY,UAAU;AACvB,aAAO,KAAK,UACN,KAAK,QAAQ,OAAO,KAAK,WAAW,KAAK,MAAM,YAAY,QAAQ,IACnE,IAAI,SAAS,YAAY,QAAQ;AAAA,IAC3C;AAAA,IACA,UAAU,YAAY,UAAU,QAAQ;AACpC,aAAO,KAAK,eACN,KAAK,aAAa,YAAY,KAAK,gBAAgB,KAAK,oBAAoB,YAAY,UAAU,MAAM,IACxG;AAAA,IACV;AAAA,IACA,OAAO,YAAY,UAAU,WAAW,WAAW,QAAQ;AACvD,aAAO,KAAK,YACN,KAAK,UAAU,SAAS,KAAK,aAAa,KAAK,iBAAiB,YAAY,UAAU,WAAW,WAAW,MAAM,IAClH,SAAS,MAAM,WAAW,SAAS;AAAA,IAC7C;AAAA,IACA,YAAY,YAAY,OAAO;AAC3B,aAAO,KAAK,iBACN,KAAK,eAAe,cAAc,KAAK,kBAAkB,KAAK,sBAAsB,YAAY,KAAK,IACrG;AAAA,IACV;AAAA,IACA,aAAa,YAAY,MAAM;AAC3B,UAAI,aAAa;AACjB,UAAI,KAAK,iBAAiB;AACtB,YAAI,KAAK,YAAY;AACjB,qBAAW,eAAe,KAAK,KAAK,iBAAiB;AAAA,QACzD;AACA,qBAAa,KAAK,gBAAgB,eAAe,KAAK,mBAAmB,KAAK,uBAAuB,YAAY,IAAI;AACrH,YAAI,CAAC;AACD,uBAAa;AAAA,MACrB,OACK;AACD,YAAI,KAAK,YAAY;AACjB,eAAK,WAAW,IAAI;AAAA,QACxB,WACS,KAAK,QAAQ,WAAW;AAC7B,4BAAkB,IAAI;AAAA,QAC1B,OACK;AACD,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QACjD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,YAAY,MAAM,WAAW,WAAW;AAC/C,aAAO,KAAK,gBACN,KAAK,cAAc,aAAa,KAAK,iBAAiB,KAAK,qBAAqB,YAAY,MAAM,WAAW,SAAS,IACtH,KAAK,SAAS,MAAM,WAAW,SAAS;AAAA,IAClD;AAAA,IACA,WAAW,YAAY,MAAM;AACzB,UAAI;AACJ,UAAI,KAAK,eAAe;AACpB,gBAAQ,KAAK,cAAc,aAAa,KAAK,iBAAiB,KAAK,qBAAqB,YAAY,IAAI;AAAA,MAC5G,OACK;AACD,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,MAAM,wBAAwB;AAAA,QACxC;AACA,gBAAQ,KAAK,SAAS,IAAI;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,YAAY,SAAS;AAGzB,UAAI;AACA,aAAK,cACD,KAAK,WAAW,UAAU,KAAK,cAAc,KAAK,kBAAkB,YAAY,OAAO;AAAA,MAC/F,SACO,KAAK;AACR,aAAK,YAAY,YAAY,GAAG;AAAA,MACpC;AAAA,IACJ;AAAA,IACA,iBAAiB,MAAM,OAAO;AAC1B,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,OAAO,IAAI;AACxB,YAAM,OAAQ,OAAO,IAAI,IAAI,OAAO;AACpC,UAAI,OAAO,GAAG;AACV,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC9D;AACA,UAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB,cAAM,UAAU;AAAA,UACZ,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,WAAW,OAAO,WAAW,IAAI;AAAA,UACjC,QAAQ;AAAA,QACZ;AACA,aAAK,QAAQ,KAAK,OAAO,OAAO;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,YAAY,MAAM,QAAQ,UAAU,SAAS,YAAY,UAAU;AAC/D,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C;AACA,WAAK,WAAW;AAChB,YAAMA,QAAO;AAEb,UAAI,SAAS,aAAa,WAAW,QAAQ,MAAM;AAC/C,aAAK,SAAS,SAAS;AAAA,MAC3B,OACK;AACD,aAAK,SAAS,WAAY;AACtB,iBAAO,SAAS,WAAW,KAAK,QAAQA,OAAM,MAAM,SAAS;AAAA,QACjE;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,OAAO,WAAW,MAAM,QAAQ,MAAM;AAClC,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AACA;AACA,UAAI;AACA,aAAK;AACL,eAAO,KAAK,KAAK,QAAQ,MAAM,QAAQ,IAAI;AAAA,MAC/C,UACA;AACI,YAAI,6BAA6B,GAAG;AAChC,8BAAoB;AAAA,QACxB;AACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,IAAI,OAAO;AACP,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,wBAAwB;AACpB,WAAK,cAAc,cAAc,UAAU;AAAA,IAC/C;AAAA,IACA,cAAc,SAAS,YAAY,YAAY;AAC3C,UAAI,KAAK,WAAW,cAAc,KAAK,WAAW,YAAY;AAC1D,aAAK,SAAS;AACd,YAAI,WAAW,cAAc;AACzB,eAAK,iBAAiB;AAAA,QAC1B;AAAA,MACJ,OACK;AACD,cAAM,IAAI,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,6BAA6B,OAAO,uBAAuB,UAAU,IAAI,aAAa,UAAU,aAAa,MAAM,EAAE,UAAU,KAAK,MAAM,IAAI;AAAA,MAC9L;AAAA,IACJ;AAAA,IACA,WAAW;AACP,UAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,aAAa,aAAa;AACxD,eAAO,KAAK,KAAK,SAAS,SAAS;AAAA,MACvC,OACK;AACD,eAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,MAC9C;AAAA,IACJ;AAAA;AAAA;AAAA,IAGA,SAAS;AACL,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AAMA,QAAM,mBAAmB,WAAW,YAAY;AAChD,QAAM,gBAAgB,WAAW,SAAS;AAC1C,QAAM,aAAa,WAAW,MAAM;AACpC,MAAI,kBAAkB,CAAC;AACvB,MAAI,4BAA4B;AAChC,MAAI;AACJ,WAAS,wBAAwB,MAAM;AACnC,QAAI,CAAC,6BAA6B;AAC9B,UAAI,OAAO,aAAa,GAAG;AACvB,sCAA8B,OAAO,aAAa,EAAE,QAAQ,CAAC;AAAA,MACjE;AAAA,IACJ;AACA,QAAI,6BAA6B;AAC7B,UAAI,aAAa,4BAA4B,UAAU;AACvD,UAAI,CAAC,YAAY;AAGb,qBAAa,4BAA4B,MAAM;AAAA,MACnD;AACA,iBAAW,KAAK,6BAA6B,IAAI;AAAA,IACrD,OACK;AACD,aAAO,gBAAgB,EAAE,MAAM,CAAC;AAAA,IACpC;AAAA,EACJ;AACA,WAAS,kBAAkB,MAAM;AAG7B,QAAI,8BAA8B,KAAK,gBAAgB,WAAW,GAAG;AAEjE,8BAAwB,mBAAmB;AAAA,IAC/C;AACA,YAAQ,gBAAgB,KAAK,IAAI;AAAA,EACrC;AACA,WAAS,sBAAsB;AAC3B,QAAI,CAAC,2BAA2B;AAC5B,kCAA4B;AAC5B,aAAO,gBAAgB,QAAQ;AAC3B,cAAM,QAAQ;AACd,0BAAkB,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI;AACA,iBAAK,KAAK,QAAQ,MAAM,MAAM,IAAI;AAAA,UACtC,SACO,OAAO;AACV,iBAAK,iBAAiB,KAAK;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,mBAAmB;AACxB,kCAA4B;AAAA,IAChC;AAAA,EACJ;AAMA,QAAM,UAAU,EAAE,MAAM,UAAU;AAClC,QAAM,eAAe,gBAAgB,aAAa,cAAc,YAAY,aAAa,UAAU,WAAW,YAAY,aAAa,UAAU;AACjJ,QAAM,YAAY,aAAa,YAAY,aAAa,YAAY;AACpE,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO;AAAA,IACT,QAAQ;AAAA,IACR,kBAAkB,MAAM;AAAA,IACxB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB;AAAA,IACA,mBAAmB,MAAM,CAAC,SAAS,WAAW,iCAAiC,CAAC;AAAA,IAChF,kBAAkB,MAAM,CAAC;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa,MAAM;AAAA,IACnB,eAAe,MAAM,CAAC;AAAA,IACtB,WAAW,MAAM;AAAA,IACjB,gBAAgB,MAAM;AAAA,IACtB,qBAAqB,MAAM;AAAA,IAC3B,YAAY,MAAM;AAAA,IAClB,kBAAkB,MAAM;AAAA,IACxB,sBAAsB,MAAM;AAAA,IAC5B,gCAAgC,MAAM;AAAA,IACtC,cAAc,MAAM;AAAA,IACpB,YAAY,MAAM,CAAC;AAAA,IACnB,YAAY,MAAM;AAAA,IAClB,qBAAqB,MAAM;AAAA,IAC3B,kBAAkB,MAAM,CAAC;AAAA,IACzB,uBAAuB,MAAM;AAAA,IAC7B,mBAAmB,MAAM;AAAA,IACzB,gBAAgB,MAAM;AAAA,IACtB;AAAA,EACJ;AACA,MAAI,oBAAoB,EAAE,QAAQ,MAAM,MAAM,IAAI,SAAS,MAAM,IAAI,EAAE;AACvE,MAAI,eAAe;AACnB,MAAI,4BAA4B;AAChC,WAAS,OAAO;AAAA,EAAE;AAClB,qBAAmB,QAAQ,MAAM;AACjC,SAAO;AACX;AAEA,SAAS,WAAW;AAUhB,QAAMC,UAAS;AACf,QAAM,iBAAiBA,QAAO,WAAW,yBAAyB,CAAC,MAAM;AACzE,MAAIA,QAAO,MAAM,MAAM,kBAAkB,OAAOA,QAAO,MAAM,EAAE,eAAe,aAAa;AACvF,UAAM,IAAI,MAAM,sBAAsB;AAAA,EAC1C;AAEA,EAAAA,QAAO,MAAM,MAAM,SAAS;AAC5B,SAAOA,QAAO,MAAM;AACxB;AAUA,IAAM,iCAAiC,OAAO;AAE9C,IAAM,uBAAuB,OAAO;AAEpC,IAAM,uBAAuB,OAAO;AAEpC,IAAM,eAAe,OAAO;AAE5B,IAAM,aAAa,MAAM,UAAU;AAEnC,IAAM,yBAAyB;AAE/B,IAAM,4BAA4B;AAElC,IAAM,iCAAiC,WAAW,sBAAsB;AAExE,IAAM,oCAAoC,WAAW,yBAAyB;AAE9E,IAAM,WAAW;AAEjB,IAAM,YAAY;AAElB,IAAM,qBAAqB,WAAW,EAAE;AACxC,SAAS,oBAAoB,UAAU,QAAQ;AAC3C,SAAO,KAAK,QAAQ,KAAK,UAAU,MAAM;AAC7C;AACA,SAAS,iCAAiC,QAAQ,UAAU,MAAM,gBAAgB,cAAc;AAC5F,SAAO,KAAK,QAAQ,kBAAkB,QAAQ,UAAU,MAAM,gBAAgB,YAAY;AAC9F;AACA,IAAM,aAAa;AACnB,IAAM,iBAAiB,OAAO,WAAW;AACzC,IAAM,iBAAiB,iBAAiB,SAAS;AACjD,IAAM,UAAW,kBAAkB,kBAAmB;AACtD,IAAM,mBAAmB;AACzB,SAAS,cAAc,MAAM,QAAQ;AACjC,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AAC/B,WAAK,CAAC,IAAI,oBAAoB,KAAK,CAAC,GAAG,SAAS,MAAM,CAAC;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,WAAW,SAAS;AACxC,QAAM,SAAS,UAAU,YAAY,MAAM;AAC3C,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAM,OAAO,QAAQ,CAAC;AACtB,UAAM,WAAW,UAAU,IAAI;AAC/B,QAAI,UAAU;AACV,YAAM,gBAAgB,+BAA+B,WAAW,IAAI;AACpE,UAAI,CAAC,mBAAmB,aAAa,GAAG;AACpC;AAAA,MACJ;AACA,gBAAU,IAAI,KAAK,CAACC,cAAa;AAC7B,cAAM,UAAU,WAAY;AACxB,iBAAOA,UAAS,MAAM,MAAM,cAAc,WAAW,SAAS,MAAM,IAAI,CAAC;AAAA,QAC7E;AACA,8BAAsB,SAASA,SAAQ;AACvC,eAAO;AAAA,MACX,GAAG,QAAQ;AAAA,IACf;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,cAAc;AACtC,MAAI,CAAC,cAAc;AACf,WAAO;AAAA,EACX;AACA,MAAI,aAAa,aAAa,OAAO;AACjC,WAAO;AAAA,EACX;AACA,SAAO,EAAE,OAAO,aAAa,QAAQ,cAAc,OAAO,aAAa,QAAQ;AACnF;AACA,IAAM,cAAc,OAAO,sBAAsB,eAAe,gBAAgB;AAGhF,IAAM,SAAS,EAAE,QAAQ,YACrB,OAAO,QAAQ,YAAY,eAC3B,QAAQ,QAAQ,SAAS,MAAM;AACnC,IAAM,YAAY,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,kBAAkB,eAAe,aAAa;AAI9F,IAAM,QAAQ,OAAO,QAAQ,YAAY,eACrC,QAAQ,QAAQ,SAAS,MAAM,sBAC/B,CAAC,eACD,CAAC,EAAE,kBAAkB,eAAe,aAAa;AACrD,IAAM,yBAAyB,CAAC;AAChC,IAAM,2BAA2B,WAAW,qBAAqB;AACjE,IAAM,SAAS,SAAU,OAAO;AAG5B,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,MAAI,kBAAkB,uBAAuB,MAAM,IAAI;AACvD,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,uBAAuB,MAAM,IAAI,IAAI,WAAW,gBAAgB,MAAM,IAAI;AAAA,EAChG;AACA,QAAM,SAAS,QAAQ,MAAM,UAAU;AACvC,QAAM,WAAW,OAAO,eAAe;AACvC,MAAI;AACJ,MAAI,aAAa,WAAW,kBAAkB,MAAM,SAAS,SAAS;AAIlE,UAAM,aAAa;AACnB,aACI,YACI,SAAS,KAAK,MAAM,WAAW,SAAS,WAAW,UAAU,WAAW,QAAQ,WAAW,OAAO,WAAW,KAAK;AAC1H,QAAI,WAAW,MAAM;AACjB,YAAM,eAAe;AAAA,IACzB;AAAA,EACJ,OACK;AACD,aAAS,YAAY,SAAS,MAAM,MAAM,SAAS;AACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,MAMX,QAAQ,wBAAwB;AAAA;AAAA,MAGhC,OAAO,WAAW;AAAA,MAAU;AAC5B,YAAM,cAAc;AAAA,IACxB,WACS,UAAU,UAAa,CAAC,QAAQ;AACrC,YAAM,eAAe;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,KAAK,MAAM,WAAW;AACzC,MAAI,OAAO,+BAA+B,KAAK,IAAI;AACnD,MAAI,CAAC,QAAQ,WAAW;AAEpB,UAAM,gBAAgB,+BAA+B,WAAW,IAAI;AACpE,QAAI,eAAe;AACf,aAAO,EAAE,YAAY,MAAM,cAAc,KAAK;AAAA,IAClD;AAAA,EACJ;AAGA,MAAI,CAAC,QAAQ,CAAC,KAAK,cAAc;AAC7B;AAAA,EACJ;AACA,QAAM,sBAAsB,WAAW,OAAO,OAAO,SAAS;AAC9D,MAAI,IAAI,eAAe,mBAAmB,KAAK,IAAI,mBAAmB,GAAG;AACrE;AAAA,EACJ;AAMA,SAAO,KAAK;AACZ,SAAO,KAAK;AACZ,QAAM,kBAAkB,KAAK;AAC7B,QAAM,kBAAkB,KAAK;AAE7B,QAAM,YAAY,KAAK,MAAM,CAAC;AAC9B,MAAI,kBAAkB,uBAAuB,SAAS;AACtD,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,uBAAuB,SAAS,IAAI,WAAW,gBAAgB,SAAS;AAAA,EAC9F;AACA,OAAK,MAAM,SAAU,UAAU;AAK3B,QAAI,SAAS;AACb,QAAI,CAAC,UAAU,QAAQ,SAAS;AAC5B,eAAS;AAAA,IACb;AACA,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,UAAM,gBAAgB,OAAO,eAAe;AAC5C,QAAI,OAAO,kBAAkB,YAAY;AACrC,aAAO,oBAAoB,WAAW,MAAM;AAAA,IAChD;AAIA,qBAAiB,KAAK,QAAQ,IAAI;AAClC,WAAO,eAAe,IAAI;AAC1B,QAAI,OAAO,aAAa,YAAY;AAChC,aAAO,iBAAiB,WAAW,QAAQ,KAAK;AAAA,IACpD;AAAA,EACJ;AAGA,OAAK,MAAM,WAAY;AAGnB,QAAI,SAAS;AACb,QAAI,CAAC,UAAU,QAAQ,SAAS;AAC5B,eAAS;AAAA,IACb;AACA,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,IACX;AACA,UAAM,WAAW,OAAO,eAAe;AACvC,QAAI,UAAU;AACV,aAAO;AAAA,IACX,WACS,iBAAiB;AAOtB,UAAI,QAAQ,gBAAgB,KAAK,IAAI;AACrC,UAAI,OAAO;AACP,aAAK,IAAI,KAAK,MAAM,KAAK;AACzB,YAAI,OAAO,OAAO,gBAAgB,MAAM,YAAY;AAChD,iBAAO,gBAAgB,IAAI;AAAA,QAC/B;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,uBAAqB,KAAK,MAAM,IAAI;AACpC,MAAI,mBAAmB,IAAI;AAC/B;AACA,SAAS,kBAAkB,KAAK,YAAY,WAAW;AACnD,MAAI,YAAY;AACZ,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,oBAAc,KAAK,OAAO,WAAW,CAAC,GAAG,SAAS;AAAA,IACtD;AAAA,EACJ,OACK;AACD,UAAM,eAAe,CAAC;AACtB,eAAW,QAAQ,KAAK;AACpB,UAAI,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM;AAC1B,qBAAa,KAAK,IAAI;AAAA,MAC1B;AAAA,IACJ;AACA,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,oBAAc,KAAK,aAAa,CAAC,GAAG,SAAS;AAAA,IACjD;AAAA,EACJ;AACJ;AACA,IAAM,sBAAsB,WAAW,kBAAkB;AAEzD,SAAS,WAAW,WAAW;AAC3B,QAAM,gBAAgB,QAAQ,SAAS;AACvC,MAAI,CAAC;AACD;AAEJ,UAAQ,WAAW,SAAS,CAAC,IAAI;AACjC,UAAQ,SAAS,IAAI,WAAY;AAC7B,UAAM,IAAI,cAAc,WAAW,SAAS;AAC5C,YAAQ,EAAE,QAAQ;AAAA,MACd,KAAK;AACD,aAAK,mBAAmB,IAAI,IAAI,cAAc;AAC9C;AAAA,MACJ,KAAK;AACD,aAAK,mBAAmB,IAAI,IAAI,cAAc,EAAE,CAAC,CAAC;AAClD;AAAA,MACJ,KAAK;AACD,aAAK,mBAAmB,IAAI,IAAI,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACxD;AAAA,MACJ,KAAK;AACD,aAAK,mBAAmB,IAAI,IAAI,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9D;AAAA,MACJ,KAAK;AACD,aAAK,mBAAmB,IAAI,IAAI,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpE;AAAA,MACJ;AACI,cAAM,IAAI,MAAM,oBAAoB;AAAA,IAC5C;AAAA,EACJ;AAEA,wBAAsB,QAAQ,SAAS,GAAG,aAAa;AACvD,QAAM,WAAW,IAAI,cAAc,WAAY;AAAA,EAAE,CAAC;AAClD,MAAI;AACJ,OAAK,QAAQ,UAAU;AAEnB,QAAI,cAAc,oBAAoB,SAAS;AAC3C;AACJ,KAAC,SAAUC,OAAM;AACb,UAAI,OAAO,SAASA,KAAI,MAAM,YAAY;AACtC,gBAAQ,SAAS,EAAE,UAAUA,KAAI,IAAI,WAAY;AAC7C,iBAAO,KAAK,mBAAmB,EAAEA,KAAI,EAAE,MAAM,KAAK,mBAAmB,GAAG,SAAS;AAAA,QACrF;AAAA,MACJ,OACK;AACD,6BAAqB,QAAQ,SAAS,EAAE,WAAWA,OAAM;AAAA,UACrD,KAAK,SAAU,IAAI;AACf,gBAAI,OAAO,OAAO,YAAY;AAC1B,mBAAK,mBAAmB,EAAEA,KAAI,IAAI,oBAAoB,IAAI,YAAY,MAAMA,KAAI;AAIhF,oCAAsB,KAAK,mBAAmB,EAAEA,KAAI,GAAG,EAAE;AAAA,YAC7D,OACK;AACD,mBAAK,mBAAmB,EAAEA,KAAI,IAAI;AAAA,YACtC;AAAA,UACJ;AAAA,UACA,KAAK,WAAY;AACb,mBAAO,KAAK,mBAAmB,EAAEA,KAAI;AAAA,UACzC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,GAAG,IAAI;AAAA,EACX;AACA,OAAK,QAAQ,eAAe;AACxB,QAAI,SAAS,eAAe,cAAc,eAAe,IAAI,GAAG;AAC5D,cAAQ,SAAS,EAAE,IAAI,IAAI,cAAc,IAAI;AAAA,IACjD;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,QAAQ,MAAM,SAAS;AACxC,MAAI,QAAQ;AACZ,SAAO,SAAS,CAAC,MAAM,eAAe,IAAI,GAAG;AACzC,YAAQ,qBAAqB,KAAK;AAAA,EACtC;AACA,MAAI,CAAC,SAAS,OAAO,IAAI,GAAG;AAExB,YAAQ;AAAA,EACZ;AACA,QAAM,eAAe,WAAW,IAAI;AACpC,MAAI,WAAW;AACf,MAAI,UAAU,EAAE,WAAW,MAAM,YAAY,MAAM,CAAC,MAAM,eAAe,YAAY,IAAI;AACrF,eAAW,MAAM,YAAY,IAAI,MAAM,IAAI;AAG3C,UAAM,OAAO,SAAS,+BAA+B,OAAO,IAAI;AAChE,QAAI,mBAAmB,IAAI,GAAG;AAC1B,YAAM,gBAAgB,QAAQ,UAAU,cAAc,IAAI;AAC1D,YAAM,IAAI,IAAI,WAAY;AACtB,eAAO,cAAc,MAAM,SAAS;AAAA,MACxC;AACA,4BAAsB,MAAM,IAAI,GAAG,QAAQ;AAAA,IAC/C;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,eAAe,KAAK,UAAU,aAAa;AAChD,MAAI,YAAY;AAChB,WAAS,aAAa,MAAM;AACxB,UAAM,OAAO,KAAK;AAClB,SAAK,KAAK,KAAK,KAAK,IAAI,WAAY;AAChC,WAAK,OAAO,MAAM,MAAM,SAAS;AAAA,IACrC;AACA,cAAU,MAAM,KAAK,QAAQ,KAAK,IAAI;AACtC,WAAO;AAAA,EACX;AACA,cAAY,YAAY,KAAK,UAAU,CAAC,aAAa,SAAUH,OAAM,MAAM;AACvE,UAAM,OAAO,YAAYA,OAAM,IAAI;AACnC,QAAI,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY;AAC3D,aAAO,iCAAiC,KAAK,MAAM,KAAK,KAAK,KAAK,GAAG,MAAM,YAAY;AAAA,IAC3F,OACK;AAED,aAAO,SAAS,MAAMA,OAAM,IAAI;AAAA,IACpC;AAAA,EACJ,CAAC;AACL;AACA,SAAS,sBAAsB,SAAS,UAAU;AAC9C,UAAQ,WAAW,kBAAkB,CAAC,IAAI;AAC9C;AACA,IAAI,qBAAqB;AACzB,IAAI,WAAW;AACf,SAAS,aAAa;AAClB,MAAI,oBAAoB;AACpB,WAAO;AAAA,EACX;AACA,uBAAqB;AACrB,MAAI;AACA,UAAM,KAAK,eAAe,UAAU;AACpC,QAAI,GAAG,QAAQ,OAAO,MAAM,MAAM,GAAG,QAAQ,UAAU,MAAM,MAAM,GAAG,QAAQ,OAAO,MAAM,IAAI;AAC3F,iBAAW;AAAA,IACf;AAAA,EACJ,SACO,OAAO;AAAA,EAAE;AAChB,SAAO;AACX;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,OAAO,UAAU;AAC5B;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU;AAC5B;AAOA,IAAM,iCAAiC;AAAA,EACnC,MAAM;AACV;AACA,IAAM,uBAAuB,CAAC;AAC9B,IAAM,gBAAgB,CAAC;AACvB,IAAM,yBAAyB,IAAI,OAAO,MAAM,qBAAqB,qBAAqB;AAC1F,IAAM,+BAA+B,WAAW,oBAAoB;AACpE,SAAS,kBAAkB,WAAW,mBAAmB;AACrD,QAAM,kBAAkB,oBAAoB,kBAAkB,SAAS,IAAI,aAAa;AACxF,QAAM,iBAAiB,oBAAoB,kBAAkB,SAAS,IAAI,aAAa;AACvF,QAAM,SAAS,qBAAqB;AACpC,QAAM,gBAAgB,qBAAqB;AAC3C,uBAAqB,SAAS,IAAI,CAAC;AACnC,uBAAqB,SAAS,EAAE,SAAS,IAAI;AAC7C,uBAAqB,SAAS,EAAE,QAAQ,IAAI;AAChD;AACA,SAAS,iBAAiBI,UAAS,KAAK,MAAM,cAAc;AACxD,QAAM,qBAAsB,gBAAgB,aAAa,OAAQ;AACjE,QAAM,wBAAyB,gBAAgB,aAAa,MAAO;AACnE,QAAM,2BAA4B,gBAAgB,aAAa,aAAc;AAC7E,QAAM,sCAAuC,gBAAgB,aAAa,SAAU;AACpF,QAAM,6BAA6B,WAAW,kBAAkB;AAChE,QAAM,4BAA4B,MAAM,qBAAqB;AAC7D,QAAM,yBAAyB;AAC/B,QAAM,gCAAgC,MAAM,yBAAyB;AACrE,QAAM,aAAa,SAAU,MAAM,QAAQ,OAAO;AAG9C,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,UAAM,WAAW,KAAK;AACtB,QAAI,OAAO,aAAa,YAAY,SAAS,aAAa;AAEtD,WAAK,WAAW,CAACC,WAAU,SAAS,YAAYA,MAAK;AACrD,WAAK,mBAAmB;AAAA,IAC5B;AAKA,QAAI;AACJ,QAAI;AACA,WAAK,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC;AAAA,IACrC,SACO,KAAK;AACR,cAAQ;AAAA,IACZ;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,OAAO,YAAY,YAAY,QAAQ,MAAM;AAIxD,YAAMH,YAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACtE,aAAO,qBAAqB,EAAE,KAAK,QAAQ,MAAM,MAAMA,WAAU,OAAO;AAAA,IAC5E;AACA,WAAO;AAAA,EACX;AACA,WAAS,eAAe,SAAS,OAAO,WAAW;AAG/C,YAAQ,SAASE,SAAQ;AACzB,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AAGA,UAAM,SAAS,WAAW,MAAM,UAAUA;AAC1C,UAAM,QAAQ,OAAO,qBAAqB,MAAM,IAAI,EAAE,YAAY,WAAW,SAAS,CAAC;AACvF,QAAI,OAAO;AACP,YAAM,SAAS,CAAC;AAGhB,UAAI,MAAM,WAAW,GAAG;AACpB,cAAM,MAAM,WAAW,MAAM,CAAC,GAAG,QAAQ,KAAK;AAC9C,eAAO,OAAO,KAAK,GAAG;AAAA,MAC1B,OACK;AAID,cAAM,YAAY,MAAM,MAAM;AAC9B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,cAAI,SAAS,MAAM,4BAA4B,MAAM,MAAM;AACvD;AAAA,UACJ;AACA,gBAAM,MAAM,WAAW,UAAU,CAAC,GAAG,QAAQ,KAAK;AAClD,iBAAO,OAAO,KAAK,GAAG;AAAA,QAC1B;AAAA,MACJ;AAGA,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,OAAO,CAAC;AAAA,MAClB,OACK;AACD,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,gBAAM,MAAM,OAAO,CAAC;AACpB,cAAI,wBAAwB,MAAM;AAC9B,kBAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,0BAA0B,SAAU,OAAO;AAC7C,WAAO,eAAe,MAAM,OAAO,KAAK;AAAA,EAC5C;AAEA,QAAM,iCAAiC,SAAU,OAAO;AACpD,WAAO,eAAe,MAAM,OAAO,IAAI;AAAA,EAC3C;AACA,WAAS,wBAAwB,KAAKE,eAAc;AAChD,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AACA,QAAI,oBAAoB;AACxB,QAAIA,iBAAgBA,cAAa,SAAS,QAAW;AACjD,0BAAoBA,cAAa;AAAA,IACrC;AACA,UAAM,kBAAkBA,iBAAgBA,cAAa;AACrD,QAAI,iBAAiB;AACrB,QAAIA,iBAAgBA,cAAa,WAAW,QAAW;AACnD,uBAAiBA,cAAa;AAAA,IAClC;AACA,QAAI,eAAe;AACnB,QAAIA,iBAAgBA,cAAa,OAAO,QAAW;AAC/C,qBAAeA,cAAa;AAAA,IAChC;AACA,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC,MAAM,eAAe,kBAAkB,GAAG;AACvD,cAAQ,qBAAqB,KAAK;AAAA,IACtC;AACA,QAAI,CAAC,SAAS,IAAI,kBAAkB,GAAG;AAEnC,cAAQ;AAAA,IACZ;AACA,QAAI,CAAC,OAAO;AACR,aAAO;AAAA,IACX;AACA,QAAI,MAAM,0BAA0B,GAAG;AACnC,aAAO;AAAA,IACX;AACA,UAAM,oBAAoBA,iBAAgBA,cAAa;AASvD,UAAM,WAAW,CAAC;AAClB,UAAM,yBAA0B,MAAM,0BAA0B,IAAI,MAAM,kBAAkB;AAC5F,UAAM,4BAA6B,MAAM,WAAW,qBAAqB,CAAC,IACtE,MAAM,qBAAqB;AAC/B,UAAM,kBAAmB,MAAM,WAAW,wBAAwB,CAAC,IAC/D,MAAM,wBAAwB;AAClC,UAAM,2BAA4B,MAAM,WAAW,mCAAmC,CAAC,IACnF,MAAM,mCAAmC;AAC7C,QAAI;AACJ,QAAIA,iBAAgBA,cAAa,SAAS;AACtC,mCAA6B,MAAM,WAAWA,cAAa,OAAO,CAAC,IAC/D,MAAMA,cAAa,OAAO;AAAA,IAClC;AAKA,aAAS,0BAA0B,SAAS,SAAS;AACjD,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,UAAI,OAAO,YAAY,WAAW;AAC9B,eAAO,EAAE,SAAS,SAAS,SAAS,KAAK;AAAA,MAC7C;AACA,UAAI,CAAC,SAAS;AACV,eAAO,EAAE,SAAS,KAAK;AAAA,MAC3B;AACA,UAAI,OAAO,YAAY,YAAY,QAAQ,YAAY,OAAO;AAC1D,eAAO,iCAAK,UAAL,EAAc,SAAS,KAAK;AAAA,MACvC;AACA,aAAO;AAAA,IACX;AACA,UAAM,uBAAuB,SAAU,MAAM;AAGzC,UAAI,SAAS,YAAY;AACrB;AAAA,MACJ;AACA,aAAO,uBAAuB,KAAK,SAAS,QAAQ,SAAS,WAAW,SAAS,UAAU,iCAAiC,yBAAyB,SAAS,OAAO;AAAA,IACzK;AAOA,UAAM,qBAAqB,SAAU,MAAM;AAIvC,UAAI,CAAC,KAAK,WAAW;AACjB,cAAM,mBAAmB,qBAAqB,KAAK,SAAS;AAC5D,YAAI;AACJ,YAAI,kBAAkB;AAClB,4BAAkB,iBAAiB,KAAK,UAAU,WAAW,SAAS;AAAA,QAC1E;AACA,cAAM,gBAAgB,mBAAmB,KAAK,OAAO,eAAe;AACpE,YAAI,eAAe;AACf,mBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,kBAAM,eAAe,cAAc,CAAC;AACpC,gBAAI,iBAAiB,MAAM;AACvB,4BAAc,OAAO,GAAG,CAAC;AAEzB,mBAAK,YAAY;AACjB,kBAAI,KAAK,qBAAqB;AAC1B,qBAAK,oBAAoB;AACzB,qBAAK,sBAAsB;AAAA,cAC/B;AACA,kBAAI,cAAc,WAAW,GAAG;AAG5B,qBAAK,aAAa;AAClB,qBAAK,OAAO,eAAe,IAAI;AAAA,cACnC;AACA;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAIA,UAAI,CAAC,KAAK,YAAY;AAClB;AAAA,MACJ;AACA,aAAO,0BAA0B,KAAK,KAAK,QAAQ,KAAK,WAAW,KAAK,UAAU,iCAAiC,yBAAyB,KAAK,OAAO;AAAA,IAC5J;AACA,UAAM,0BAA0B,SAAU,MAAM;AAC5C,aAAO,uBAAuB,KAAK,SAAS,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,IACzG;AACA,UAAM,wBAAwB,SAAU,MAAM;AAC1C,aAAO,2BAA2B,KAAK,SAAS,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC7G;AACA,UAAM,wBAAwB,SAAU,MAAM;AAC1C,aAAO,0BAA0B,KAAK,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,OAAO;AAAA,IAChG;AACA,UAAM,iBAAiB,oBAAoB,uBAAuB;AAClE,UAAM,eAAe,oBAAoB,qBAAqB;AAC9D,UAAM,gCAAgC,SAAU,MAAM,UAAU;AAC5D,YAAM,iBAAiB,OAAO;AAC9B,aAAS,mBAAmB,cAAc,KAAK,aAAa,YACvD,mBAAmB,YAAY,KAAK,qBAAqB;AAAA,IAClE;AACA,UAAM,UAAUA,eAAc,QAAQ;AACtC,UAAM,kBAAkB,KAAK,WAAW,kBAAkB,CAAC;AAC3D,UAAM,gBAAgBF,SAAQ,WAAW,gBAAgB,CAAC;AAC1D,aAAS,yBAAyB,SAAS;AACvC,UAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AAIjD,cAAM,aAAa,mBAAK;AAUxB,YAAI,QAAQ,QAAQ;AAChB,qBAAW,SAAS,QAAQ;AAAA,QAChC;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB,SAAU,gBAAgB,WAAW,kBAAkB,gBAAgBG,gBAAe,OAAO,UAAU,OAAO;AAClI,aAAO,WAAY;AACf,cAAM,SAAS,QAAQH;AACvB,YAAI,YAAY,UAAU,CAAC;AAC3B,YAAIE,iBAAgBA,cAAa,mBAAmB;AAChD,sBAAYA,cAAa,kBAAkB,SAAS;AAAA,QACxD;AACA,YAAI,WAAW,UAAU,CAAC;AAC1B,YAAI,CAAC,UAAU;AACX,iBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,QAC/C;AACA,YAAI,UAAU,cAAc,qBAAqB;AAE7C,iBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,QAC/C;AAGA,YAAI,wBAAwB;AAC5B,YAAI,OAAO,aAAa,YAAY;AAIhC,cAAI,CAAC,SAAS,aAAa;AACvB,mBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,UAC/C;AACA,kCAAwB;AAAA,QAC5B;AACA,YAAI,mBAAmB,CAAC,gBAAgB,gBAAgB,UAAU,QAAQ,SAAS,GAAG;AAClF;AAAA,QACJ;AACA,cAAM,UAAU,CAAC,CAAC,iBAAiB,cAAc,QAAQ,SAAS,MAAM;AACxE,cAAM,UAAU,yBAAyB,0BAA0B,UAAU,CAAC,GAAG,OAAO,CAAC;AACzF,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ,SAAS;AAEjB;AAAA,QACJ;AACA,YAAI,iBAAiB;AAEjB,mBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,gBAAI,cAAc,gBAAgB,CAAC,GAAG;AAClC,kBAAI,SAAS;AACT,uBAAO,eAAe,KAAK,QAAQ,WAAW,UAAU,OAAO;AAAA,cACnE,OACK;AACD,uBAAO,eAAe,MAAM,MAAM,SAAS;AAAA,cAC/C;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,UAAU,CAAC,UAAU,QAAQ,OAAO,YAAY,YAAY,OAAO,QAAQ;AACjF,cAAM,OAAO,WAAW,OAAO,YAAY,WAAW,QAAQ,OAAO;AACrE,cAAM,OAAO,KAAK;AAClB,YAAI,mBAAmB,qBAAqB,SAAS;AACrD,YAAI,CAAC,kBAAkB;AACnB,4BAAkB,WAAW,iBAAiB;AAC9C,6BAAmB,qBAAqB,SAAS;AAAA,QACrD;AACA,cAAM,kBAAkB,iBAAiB,UAAU,WAAW,SAAS;AACvE,YAAI,gBAAgB,OAAO,eAAe;AAC1C,YAAI,aAAa;AACjB,YAAI,eAAe;AAEf,uBAAa;AACb,cAAI,gBAAgB;AAChB,qBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,kBAAI,QAAQ,cAAc,CAAC,GAAG,QAAQ,GAAG;AAErC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,OACK;AACD,0BAAgB,OAAO,eAAe,IAAI,CAAC;AAAA,QAC/C;AACA,YAAI;AACJ,cAAM,kBAAkB,OAAO,YAAY,MAAM;AACjD,cAAM,eAAe,cAAc,eAAe;AAClD,YAAI,cAAc;AACd,mBAAS,aAAa,SAAS;AAAA,QACnC;AACA,YAAI,CAAC,QAAQ;AACT,mBACI,kBACI,aACC,oBAAoB,kBAAkB,SAAS,IAAI;AAAA,QAChE;AAMA,iBAAS,UAAU;AACnB,YAAI,MAAM;AAIN,mBAAS,QAAQ,OAAO;AAAA,QAC5B;AACA,iBAAS,SAAS;AAClB,iBAAS,UAAU;AACnB,iBAAS,YAAY;AACrB,iBAAS,aAAa;AACtB,cAAM,OAAO,oBAAoB,iCAAiC;AAElE,YAAI,MAAM;AACN,eAAK,WAAW;AAAA,QACpB;AACA,YAAI,QAAQ;AAIR,mBAAS,QAAQ,SAAS;AAAA,QAC9B;AAKA,cAAM,OAAO,KAAK,kBAAkB,QAAQ,UAAU,MAAM,kBAAkB,cAAc;AAC5F,YAAI,QAAQ;AAER,mBAAS,QAAQ,SAAS;AAI1B,gBAAM,UAAU,MAAM,KAAK,KAAK,WAAW,IAAI;AAC/C,yBAAe,KAAK,QAAQ,SAAS,SAAS,EAAE,MAAM,KAAK,CAAC;AAK5D,eAAK,sBAAsB,MAAM,OAAO,oBAAoB,SAAS,OAAO;AAAA,QAChF;AAGA,iBAAS,SAAS;AAElB,YAAI,MAAM;AACN,eAAK,WAAW;AAAA,QACpB;AAGA,YAAI,MAAM;AACN,mBAAS,QAAQ,OAAO;AAAA,QAC5B;AACA,YAAI,OAAO,KAAK,YAAY,WAAW;AAInC,eAAK,UAAU;AAAA,QACnB;AACA,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,YAAI,uBAAuB;AAEvB,eAAK,mBAAmB;AAAA,QAC5B;AACA,YAAI,CAAC,SAAS;AACV,wBAAc,KAAK,IAAI;AAAA,QAC3B,OACK;AACD,wBAAc,QAAQ,IAAI;AAAA,QAC9B;AACA,YAAIC,eAAc;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,IAAI,gBAAgB,wBAAwB,2BAA2B,gBAAgB,cAAc,YAAY;AACzI,QAAI,4BAA4B;AAC5B,YAAM,sBAAsB,IAAI,gBAAgB,4BAA4B,+BAA+B,uBAAuB,cAAc,cAAc,IAAI;AAAA,IACtK;AACA,UAAM,qBAAqB,IAAI,WAAY;AACvC,YAAM,SAAS,QAAQH;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAIE,iBAAgBA,cAAa,mBAAmB;AAChD,oBAAYA,cAAa,kBAAkB,SAAS;AAAA,MACxD;AACA,YAAM,UAAU,UAAU,CAAC;AAC3B,YAAM,UAAU,CAAC,UAAU,QAAQ,OAAO,YAAY,YAAY,OAAO,QAAQ;AACjF,YAAM,WAAW,UAAU,CAAC;AAC5B,UAAI,CAAC,UAAU;AACX,eAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,MAC1D;AACA,UAAI,mBACA,CAAC,gBAAgB,2BAA2B,UAAU,QAAQ,SAAS,GAAG;AAC1E;AAAA,MACJ;AACA,YAAM,mBAAmB,qBAAqB,SAAS;AACvD,UAAI;AACJ,UAAI,kBAAkB;AAClB,0BAAkB,iBAAiB,UAAU,WAAW,SAAS;AAAA,MACrE;AACA,YAAM,gBAAgB,mBAAmB,OAAO,eAAe;AAK/D,UAAI,eAAe;AACf,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,eAAe,cAAc,CAAC;AACpC,cAAI,QAAQ,cAAc,QAAQ,GAAG;AACjC,0BAAc,OAAO,GAAG,CAAC;AAEzB,yBAAa,YAAY;AACzB,gBAAI,cAAc,WAAW,GAAG;AAG5B,2BAAa,aAAa;AAC1B,qBAAO,eAAe,IAAI;AAM1B,kBAAI,CAAC,WAAW,OAAO,cAAc,UAAU;AAC3C,sBAAM,mBAAmB,qBAAqB,gBAAgB;AAC9D,uBAAO,gBAAgB,IAAI;AAAA,cAC/B;AAAA,YACJ;AAMA,yBAAa,KAAK,WAAW,YAAY;AACzC,gBAAI,cAAc;AACd,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAOA,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IAC1D;AACA,UAAM,wBAAwB,IAAI,WAAY;AAC1C,YAAM,SAAS,QAAQF;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAIE,iBAAgBA,cAAa,mBAAmB;AAChD,oBAAYA,cAAa,kBAAkB,SAAS;AAAA,MACxD;AACA,YAAM,YAAY,CAAC;AACnB,YAAM,QAAQ,eAAe,QAAQ,oBAAoB,kBAAkB,SAAS,IAAI,SAAS;AACjG,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,kBAAU,KAAK,QAAQ;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AACA,UAAM,mCAAmC,IAAI,WAAY;AACrD,YAAM,SAAS,QAAQF;AACvB,UAAI,YAAY,UAAU,CAAC;AAC3B,UAAI,CAAC,WAAW;AACZ,cAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,gBAAM,OAAO,KAAK,CAAC;AACnB,gBAAM,QAAQ,uBAAuB,KAAK,IAAI;AAC9C,cAAI,UAAU,SAAS,MAAM,CAAC;AAK9B,cAAI,WAAW,YAAY,kBAAkB;AACzC,iBAAK,mCAAmC,EAAE,KAAK,MAAM,OAAO;AAAA,UAChE;AAAA,QACJ;AAEA,aAAK,mCAAmC,EAAE,KAAK,MAAM,gBAAgB;AAAA,MACzE,OACK;AACD,YAAIE,iBAAgBA,cAAa,mBAAmB;AAChD,sBAAYA,cAAa,kBAAkB,SAAS;AAAA,QACxD;AACA,cAAM,mBAAmB,qBAAqB,SAAS;AACvD,YAAI,kBAAkB;AAClB,gBAAM,kBAAkB,iBAAiB,SAAS;AAClD,gBAAM,yBAAyB,iBAAiB,QAAQ;AACxD,gBAAM,QAAQ,OAAO,eAAe;AACpC,gBAAM,eAAe,OAAO,sBAAsB;AAClD,cAAI,OAAO;AACP,kBAAM,cAAc,MAAM,MAAM;AAChC,qBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,oBAAM,OAAO,YAAY,CAAC;AAC1B,kBAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,mBAAK,qBAAqB,EAAE,KAAK,MAAM,WAAW,UAAU,KAAK,OAAO;AAAA,YAC5E;AAAA,UACJ;AACA,cAAI,cAAc;AACd,kBAAM,cAAc,aAAa,MAAM;AACvC,qBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,oBAAM,OAAO,YAAY,CAAC;AAC1B,kBAAI,WAAW,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACpE,mBAAK,qBAAqB,EAAE,KAAK,MAAM,WAAW,UAAU,KAAK,OAAO;AAAA,YAC5E;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,cAAc;AACd,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,0BAAsB,MAAM,kBAAkB,GAAG,sBAAsB;AACvE,0BAAsB,MAAM,qBAAqB,GAAG,yBAAyB;AAC7E,QAAI,0BAA0B;AAC1B,4BAAsB,MAAM,mCAAmC,GAAG,wBAAwB;AAAA,IAC9F;AACA,QAAI,iBAAiB;AACjB,4BAAsB,MAAM,wBAAwB,GAAG,eAAe;AAAA,IAC1E;AACA,WAAO;AAAA,EACX;AACA,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAQ,CAAC,IAAI,wBAAwB,KAAK,CAAC,GAAG,YAAY;AAAA,EAC9D;AACA,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,WAAW;AACvC,MAAI,CAAC,WAAW;AACZ,UAAM,aAAa,CAAC;AACpB,aAAS,QAAQ,QAAQ;AACrB,YAAM,QAAQ,uBAAuB,KAAK,IAAI;AAC9C,UAAI,UAAU,SAAS,MAAM,CAAC;AAC9B,UAAI,YAAY,CAAC,aAAa,YAAY,YAAY;AAClD,cAAM,QAAQ,OAAO,IAAI;AACzB,YAAI,OAAO;AACP,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,uBAAW,KAAK,MAAM,CAAC,CAAC;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,qBAAqB,SAAS;AACpD,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,SAAS;AAC3B,sBAAkB,qBAAqB,SAAS;AAAA,EACpD;AACA,QAAM,oBAAoB,OAAO,gBAAgB,SAAS,CAAC;AAC3D,QAAM,mBAAmB,OAAO,gBAAgB,QAAQ,CAAC;AACzD,MAAI,CAAC,mBAAmB;AACpB,WAAO,mBAAmB,iBAAiB,MAAM,IAAI,CAAC;AAAA,EAC1D,OACK;AACD,WAAO,mBACD,kBAAkB,OAAO,gBAAgB,IACzC,kBAAkB,MAAM;AAAA,EAClC;AACJ;AACA,SAAS,oBAAoBL,SAAQ,KAAK;AACtC,QAAM,QAAQA,QAAO,OAAO;AAC5B,MAAI,SAAS,MAAM,WAAW;AAC1B,QAAI,YAAY,MAAM,WAAW,4BAA4B,CAAC,aAAa,SAAUD,OAAM,MAAM;AAC7F,MAAAA,MAAK,4BAA4B,IAAI;AAIrC,kBAAY,SAAS,MAAMA,OAAM,IAAI;AAAA,IACzC,CAAC;AAAA,EACL;AACJ;AAMA,SAAS,oBAAoBC,SAAQ,KAAK;AACtC,MAAI,YAAYA,SAAQ,kBAAkB,CAAC,aAAa;AACpD,WAAO,SAAUD,OAAM,MAAM;AACzB,WAAK,QAAQ,kBAAkB,kBAAkB,KAAK,CAAC,CAAC;AAAA,IAC5D;AAAA,EACJ,CAAC;AACL;AAMA,IAAM,aAAa,WAAW,UAAU;AACxC,SAAS,WAAWQ,SAAQ,SAAS,YAAY,YAAY;AACzD,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,aAAW;AACX,gBAAc;AACd,QAAM,kBAAkB,CAAC;AACzB,WAAS,aAAa,MAAM;AACxB,UAAM,OAAO,KAAK;AAClB,SAAK,KAAK,CAAC,IAAI,WAAY;AACvB,aAAO,KAAK,OAAO,MAAM,MAAM,SAAS;AAAA,IAC5C;AACA,UAAM,aAAa,UAAU,MAAMA,SAAQ,KAAK,IAAI;AAIpD,QAAI,SAAS,UAAU,GAAG;AACtB,WAAK,WAAW;AAAA,IACpB,OACK;AACD,WAAK,SAAS;AAEd,WAAK,gBAAgB,WAAW,WAAW,OAAO;AAAA,IACtD;AACA,WAAO;AAAA,EACX;AACA,WAAS,UAAU,MAAM;AACrB,UAAM,EAAE,QAAQ,SAAS,IAAI,KAAK;AAClC,WAAO,YAAY,KAAKA,SAAQ,UAAU,QAAQ;AAAA,EACtD;AACA,cAAY,YAAYA,SAAQ,SAAS,CAAC,aAAa,SAAUR,OAAM,MAAM;AACzE,QAAI,WAAW,KAAK,CAAC,CAAC,GAAG;AACrB,YAAM,UAAU;AAAA,QACZ,eAAe;AAAA,QACf,YAAY,eAAe;AAAA,QAC3B,OAAO,eAAe,aAAa,eAAe,aAAa,KAAK,CAAC,KAAK,IAAI;AAAA,QAC9E;AAAA,MACJ;AACA,YAAM,WAAW,KAAK,CAAC;AACvB,WAAK,CAAC,IAAI,SAAS,QAAQ;AACvB,YAAI;AACA,iBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,QACzC,UACA;AAQI,gBAAM,EAAE,QAAAS,SAAQ,UAAAC,WAAU,YAAAC,aAAY,eAAAC,eAAc,IAAI;AACxD,cAAI,CAACD,eAAc,CAACC,gBAAe;AAC/B,gBAAIF,WAAU;AAGV,qBAAO,gBAAgBA,SAAQ;AAAA,YACnC,WACSD,SAAQ;AAGb,cAAAA,QAAO,UAAU,IAAI;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,OAAO,iCAAiC,SAAS,KAAK,CAAC,GAAG,SAAS,cAAc,SAAS;AAChG,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,YAAM,EAAE,UAAU,QAAQ,eAAe,WAAW,IAAI,KAAK;AAC7D,UAAI,UAAU;AAGV,wBAAgB,QAAQ,IAAI;AAAA,MAChC,WACS,QAAQ;AAGb,eAAO,UAAU,IAAI;AACrB,YAAI,iBAAiB,CAAC,YAAY;AAC9B,gBAAM,kBAAkB,OAAO;AAC/B,iBAAO,UAAU,WAAY;AACzB,kBAAM,EAAE,MAAM,MAAM,IAAI;AACxB,gBAAI,UAAU,gBAAgB;AAC1B,mBAAK,SAAS;AACd,mBAAK,iBAAiB,MAAM,CAAC;AAAA,YACjC,WACS,UAAU,WAAW;AAC1B,mBAAK,SAAS;AAAA,YAClB;AACA,mBAAO,gBAAgB,KAAK,IAAI;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,UAAU,YAAY;AAAA,IACjC,OACK;AAED,aAAO,SAAS,MAAMD,SAAQ,IAAI;AAAA,IACtC;AAAA,EACJ,CAAC;AACD,gBAAc,YAAYA,SAAQ,YAAY,CAAC,aAAa,SAAUR,OAAM,MAAM;AAC9E,UAAM,KAAK,KAAK,CAAC;AACjB,QAAI;AACJ,QAAI,SAAS,EAAE,GAAG;AAEd,aAAO,gBAAgB,EAAE;AACzB,aAAO,gBAAgB,EAAE;AAAA,IAC7B,OACK;AAED,aAAO,KAAK,UAAU;AACtB,UAAI,MAAM;AACN,WAAG,UAAU,IAAI;AAAA,MACrB,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,MAAM,MAAM;AACZ,UAAI,KAAK,UAAU;AAEf,aAAK,KAAK,WAAW,IAAI;AAAA,MAC7B;AAAA,IACJ,OACK;AAED,eAAS,MAAMQ,SAAQ,IAAI;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,oBAAoBJ,UAAS,KAAK;AACvC,QAAM,EAAE,WAAAS,YAAW,OAAAC,OAAM,IAAI,IAAI,iBAAiB;AAClD,MAAK,CAACD,cAAa,CAACC,UAAU,CAACV,SAAQ,gBAAgB,KAAK,EAAE,oBAAoBA,WAAU;AACxF;AAAA,EACJ;AAEA,QAAM,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAI,eAAe,KAAKA,SAAQ,gBAAgB,kBAAkB,UAAU,SAAS;AACzF;AAEA,SAAS,iBAAiBA,UAAS,KAAK;AACpC,MAAI,KAAK,IAAI,OAAO,kBAAkB,CAAC,GAAG;AAEtC;AAAA,EACJ;AACA,QAAM,EAAE,YAAY,sBAAAW,uBAAsB,UAAAC,WAAU,WAAAC,YAAW,oBAAAC,oBAAmB,IAAI,IAAI,iBAAiB;AAE3G,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAM,YAAY,WAAW,CAAC;AAC9B,UAAM,iBAAiB,YAAYD;AACnC,UAAM,gBAAgB,YAAYD;AAClC,UAAM,SAASE,sBAAqB;AACpC,UAAM,gBAAgBA,sBAAqB;AAC3C,IAAAH,sBAAqB,SAAS,IAAI,CAAC;AACnC,IAAAA,sBAAqB,SAAS,EAAEE,UAAS,IAAI;AAC7C,IAAAF,sBAAqB,SAAS,EAAEC,SAAQ,IAAI;AAAA,EAChD;AACA,QAAM,eAAeZ,SAAQ,aAAa;AAC1C,MAAI,CAAC,gBAAgB,CAAC,aAAa,WAAW;AAC1C;AAAA,EACJ;AACA,MAAI,iBAAiBA,UAAS,KAAK,CAAC,gBAAgB,aAAa,SAAS,CAAC;AAC3E,SAAO;AACX;AACA,SAAS,WAAWH,SAAQ,KAAK;AAC7B,MAAI,oBAAoBA,SAAQ,GAAG;AACvC;AAMA,SAAS,iBAAiB,QAAQ,cAAc,kBAAkB;AAC9D,MAAI,CAAC,oBAAoB,iBAAiB,WAAW,GAAG;AACpD,WAAO;AAAA,EACX;AACA,QAAM,MAAM,iBAAiB,OAAO,CAAC,OAAO,GAAG,WAAW,MAAM;AAChE,MAAI,IAAI,WAAW,GAAG;AAClB,WAAO;AAAA,EACX;AACA,QAAM,yBAAyB,IAAI,CAAC,EAAE;AACtC,SAAO,aAAa,OAAO,CAAC,OAAO,uBAAuB,QAAQ,EAAE,MAAM,EAAE;AAChF;AACA,SAAS,wBAAwB,QAAQ,cAAc,kBAAkB,WAAW;AAGhF,MAAI,CAAC,QAAQ;AACT;AAAA,EACJ;AACA,QAAM,qBAAqB,iBAAiB,QAAQ,cAAc,gBAAgB;AAClF,oBAAkB,QAAQ,oBAAoB,SAAS;AAC3D;AAKA,SAAS,gBAAgB,QAAQ;AAC7B,SAAO,OAAO,oBAAoB,MAAM,EACnC,OAAO,CAAC,SAAS,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,CAAC,EACzD,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC,CAAC;AACxC;AACA,SAAS,wBAAwB,KAAKG,UAAS;AAC3C,MAAI,UAAU,CAAC,OAAO;AAClB;AAAA,EACJ;AACA,MAAI,KAAK,IAAI,OAAO,aAAa,CAAC,GAAG;AAEjC;AAAA,EACJ;AACA,QAAM,mBAAmBA,SAAQ,6BAA6B;AAE9D,MAAI,eAAe,CAAC;AACpB,MAAI,WAAW;AACX,UAAMe,kBAAiB;AACvB,mBAAe,aAAa,OAAO;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,UAAM,wBAAwB,CAAC;AAK/B,4BAAwBA,iBAAgB,gBAAgBA,eAAc,GAAG,mBAAmB,iBAAiB,OAAO,qBAAqB,IAAI,kBAAkB,qBAAqBA,eAAc,CAAC;AAAA,EACvM;AACA,iBAAe,aAAa,OAAO;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,UAAM,SAASf,SAAQ,aAAa,CAAC,CAAC;AACtC,YAAQ,aACJ,wBAAwB,OAAO,WAAW,gBAAgB,OAAO,SAAS,GAAG,gBAAgB;AAAA,EACrG;AACJ;AAMA,SAAS,aAAagB,OAAM;AACxB,EAAAA,MAAK,aAAa,UAAU,CAACnB,YAAW;AACpC,UAAM,cAAcA,QAAOmB,MAAK,WAAW,aAAa,CAAC;AACzD,QAAI,aAAa;AACb,kBAAY;AAAA,IAChB;AAAA,EACJ,CAAC;AACD,EAAAA,MAAK,aAAa,UAAU,CAACnB,YAAW;AACpC,UAAM,MAAM;AACZ,UAAM,QAAQ;AACd,eAAWA,SAAQ,KAAK,OAAO,SAAS;AACxC,eAAWA,SAAQ,KAAK,OAAO,UAAU;AACzC,eAAWA,SAAQ,KAAK,OAAO,WAAW;AAAA,EAC9C,CAAC;AACD,EAAAmB,MAAK,aAAa,yBAAyB,CAACnB,YAAW;AACnD,eAAWA,SAAQ,WAAW,UAAU,gBAAgB;AACxD,eAAWA,SAAQ,cAAc,aAAa,gBAAgB;AAC9D,eAAWA,SAAQ,iBAAiB,gBAAgB,gBAAgB;AAAA,EACxE,CAAC;AACD,EAAAmB,MAAK,aAAa,YAAY,CAACnB,SAAQmB,UAAS;AAC5C,UAAM,kBAAkB,CAAC,SAAS,UAAU,SAAS;AACrD,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,YAAM,OAAO,gBAAgB,CAAC;AAC9B,kBAAYnB,SAAQ,MAAM,CAAC,UAAU,QAAQoB,UAAS;AAClD,eAAO,SAAU,GAAG,MAAM;AACtB,iBAAOD,MAAK,QAAQ,IAAI,UAAUnB,SAAQ,MAAMoB,KAAI;AAAA,QACxD;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACD,EAAAD,MAAK,aAAa,eAAe,CAACnB,SAAQmB,OAAM,QAAQ;AACpD,eAAWnB,SAAQ,GAAG;AACtB,qBAAiBA,SAAQ,GAAG;AAE5B,UAAM,4BAA4BA,QAAO,2BAA2B;AACpE,QAAI,6BAA6B,0BAA0B,WAAW;AAClE,UAAI,iBAAiBA,SAAQ,KAAK,CAAC,0BAA0B,SAAS,CAAC;AAAA,IAC3E;AAAA,EACJ,CAAC;AACD,EAAAmB,MAAK,aAAa,oBAAoB,CAACnB,SAAQmB,OAAM,QAAQ;AACzD,eAAW,kBAAkB;AAC7B,eAAW,wBAAwB;AAAA,EACvC,CAAC;AACD,EAAAA,MAAK,aAAa,wBAAwB,CAACnB,SAAQmB,OAAM,QAAQ;AAC7D,eAAW,sBAAsB;AAAA,EACrC,CAAC;AACD,EAAAA,MAAK,aAAa,cAAc,CAACnB,SAAQmB,OAAM,QAAQ;AACnD,eAAW,YAAY;AAAA,EAC3B,CAAC;AACD,EAAAA,MAAK,aAAa,eAAe,CAACnB,SAAQmB,OAAM,QAAQ;AACpD,4BAAwB,KAAKnB,OAAM;AAAA,EACvC,CAAC;AACD,EAAAmB,MAAK,aAAa,kBAAkB,CAACnB,SAAQmB,OAAM,QAAQ;AACvD,wBAAoBnB,SAAQ,GAAG;AAAA,EACnC,CAAC;AACD,EAAAmB,MAAK,aAAa,OAAO,CAACnB,SAAQmB,UAAS;AAEvC,aAASnB,OAAM;AACf,UAAM,WAAW,WAAW,SAAS;AACrC,UAAM,WAAW,WAAW,SAAS;AACrC,UAAM,eAAe,WAAW,aAAa;AAC7C,UAAM,gBAAgB,WAAW,cAAc;AAC/C,UAAM,UAAU,WAAW,QAAQ;AACnC,UAAM,6BAA6B,WAAW,yBAAyB;AACvE,aAAS,SAASO,SAAQ;AACtB,YAAM,iBAAiBA,QAAO,gBAAgB;AAC9C,UAAI,CAAC,gBAAgB;AAEjB;AAAA,MACJ;AACA,YAAM,0BAA0B,eAAe;AAC/C,eAAS,gBAAgB,QAAQ;AAC7B,eAAO,OAAO,QAAQ;AAAA,MAC1B;AACA,UAAI,iBAAiB,wBAAwB,8BAA8B;AAC3E,UAAI,oBAAoB,wBAAwB,iCAAiC;AACjF,UAAI,CAAC,gBAAgB;AACjB,cAAM,4BAA4BA,QAAO,2BAA2B;AACpE,YAAI,2BAA2B;AAC3B,gBAAM,qCAAqC,0BAA0B;AACrE,2BAAiB,mCAAmC,8BAA8B;AAClF,8BAAoB,mCAAmC,iCAAiC;AAAA,QAC5F;AAAA,MACJ;AACA,YAAM,qBAAqB;AAC3B,YAAM,YAAY;AAClB,eAAS,aAAa,MAAM;AACxB,cAAM,OAAO,KAAK;AAClB,cAAM,SAAS,KAAK;AACpB,eAAO,aAAa,IAAI;AACxB,eAAO,0BAA0B,IAAI;AAErC,cAAM,WAAW,OAAO,YAAY;AACpC,YAAI,CAAC,gBAAgB;AACjB,2BAAiB,OAAO,8BAA8B;AACtD,8BAAoB,OAAO,iCAAiC;AAAA,QAChE;AACA,YAAI,UAAU;AACV,4BAAkB,KAAK,QAAQ,oBAAoB,QAAQ;AAAA,QAC/D;AACA,cAAM,cAAe,OAAO,YAAY,IAAI,MAAM;AAC9C,cAAI,OAAO,eAAe,OAAO,MAAM;AAGnC,gBAAI,CAAC,KAAK,WAAW,OAAO,aAAa,KAAK,KAAK,UAAU,WAAW;AAQpE,oBAAM,YAAY,OAAOY,MAAK,WAAW,WAAW,CAAC;AACrD,kBAAI,OAAO,WAAW,KAAK,aAAa,UAAU,SAAS,GAAG;AAC1D,sBAAM,YAAY,KAAK;AACvB,qBAAK,SAAS,WAAY;AAGtB,wBAAME,aAAY,OAAOF,MAAK,WAAW,WAAW,CAAC;AACrD,2BAAS,IAAI,GAAG,IAAIE,WAAU,QAAQ,KAAK;AACvC,wBAAIA,WAAU,CAAC,MAAM,MAAM;AACvB,sBAAAA,WAAU,OAAO,GAAG,CAAC;AAAA,oBACzB;AAAA,kBACJ;AACA,sBAAI,CAAC,KAAK,WAAW,KAAK,UAAU,WAAW;AAC3C,8BAAU,KAAK,IAAI;AAAA,kBACvB;AAAA,gBACJ;AACA,0BAAU,KAAK,IAAI;AAAA,cACvB,OACK;AACD,qBAAK,OAAO;AAAA,cAChB;AAAA,YACJ,WACS,CAAC,KAAK,WAAW,OAAO,aAAa,MAAM,OAAO;AAEvD,qBAAO,0BAA0B,IAAI;AAAA,YACzC;AAAA,UACJ;AAAA,QACJ;AACA,uBAAe,KAAK,QAAQ,oBAAoB,WAAW;AAC3D,cAAM,aAAa,OAAO,QAAQ;AAClC,YAAI,CAAC,YAAY;AACb,iBAAO,QAAQ,IAAI;AAAA,QACvB;AACA,mBAAW,MAAM,QAAQ,KAAK,IAAI;AAClC,eAAO,aAAa,IAAI;AACxB,eAAO;AAAA,MACX;AACA,eAAS,sBAAsB;AAAA,MAAE;AACjC,eAAS,UAAU,MAAM;AACrB,cAAM,OAAO,KAAK;AAGlB,aAAK,UAAU;AACf,eAAO,YAAY,MAAM,KAAK,QAAQ,KAAK,IAAI;AAAA,MACnD;AACA,YAAM,aAAa,YAAY,yBAAyB,QAAQ,MAAM,SAAUtB,OAAM,MAAM;AACxF,QAAAA,MAAK,QAAQ,IAAI,KAAK,CAAC,KAAK;AAC5B,QAAAA,MAAK,OAAO,IAAI,KAAK,CAAC;AACtB,eAAO,WAAW,MAAMA,OAAM,IAAI;AAAA,MACtC,CAAC;AACD,YAAM,wBAAwB;AAC9B,YAAM,oBAAoB,WAAW,mBAAmB;AACxD,YAAM,sBAAsB,WAAW,qBAAqB;AAC5D,YAAM,aAAa,YAAY,yBAAyB,QAAQ,MAAM,SAAUA,OAAM,MAAM;AACxF,YAAIoB,MAAK,QAAQ,mBAAmB,MAAM,MAAM;AAI5C,iBAAO,WAAW,MAAMpB,OAAM,IAAI;AAAA,QACtC;AACA,YAAIA,MAAK,QAAQ,GAAG;AAEhB,iBAAO,WAAW,MAAMA,OAAM,IAAI;AAAA,QACtC,OACK;AACD,gBAAM,UAAU;AAAA,YACZ,QAAQA;AAAA,YACR,KAAKA,MAAK,OAAO;AAAA,YACjB,YAAY;AAAA,YACZ;AAAA,YACA,SAAS;AAAA,UACb;AACA,gBAAM,OAAO,iCAAiC,uBAAuB,qBAAqB,SAAS,cAAc,SAAS;AAC1H,cAAIA,SACAA,MAAK,0BAA0B,MAAM,QACrC,CAAC,QAAQ,WACT,KAAK,UAAU,WAAW;AAI1B,iBAAK,OAAO;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,YAAM,cAAc,YAAY,yBAAyB,SAAS,MAAM,SAAUA,OAAM,MAAM;AAC1F,cAAM,OAAO,gBAAgBA,KAAI;AACjC,YAAI,QAAQ,OAAO,KAAK,QAAQ,UAAU;AAKtC,cAAI,KAAK,YAAY,QAAS,KAAK,QAAQ,KAAK,KAAK,SAAU;AAC3D;AAAA,UACJ;AACA,eAAK,KAAK,WAAW,IAAI;AAAA,QAC7B,WACSoB,MAAK,QAAQ,iBAAiB,MAAM,MAAM;AAE/C,iBAAO,YAAY,MAAMpB,OAAM,IAAI;AAAA,QACvC;AAAA,MAIJ,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACD,EAAAoB,MAAK,aAAa,eAAe,CAACnB,YAAW;AAEzC,QAAIA,QAAO,WAAW,KAAKA,QAAO,WAAW,EAAE,aAAa;AACxD,qBAAeA,QAAO,WAAW,EAAE,aAAa,CAAC,sBAAsB,eAAe,CAAC;AAAA,IAC3F;AAAA,EACJ,CAAC;AACD,EAAAmB,MAAK,aAAa,yBAAyB,CAACnB,SAAQmB,UAAS;AAEzD,aAAS,4BAA4B,SAAS;AAC1C,aAAO,SAAU,GAAG;AAChB,cAAM,aAAa,eAAenB,SAAQ,OAAO;AACjD,mBAAW,QAAQ,CAAC,cAAc;AAG9B,gBAAM,wBAAwBA,QAAO,uBAAuB;AAC5D,cAAI,uBAAuB;AACvB,kBAAM,MAAM,IAAI,sBAAsB,SAAS;AAAA,cAC3C,SAAS,EAAE;AAAA,cACX,QAAQ,EAAE;AAAA,YACd,CAAC;AACD,sBAAU,OAAO,GAAG;AAAA,UACxB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAIA,QAAO,uBAAuB,GAAG;AACjC,MAAAmB,MAAK,WAAW,kCAAkC,CAAC,IAC/C,4BAA4B,oBAAoB;AACpD,MAAAA,MAAK,WAAW,yBAAyB,CAAC,IACtC,4BAA4B,kBAAkB;AAAA,IACtD;AAAA,EACJ,CAAC;AACD,EAAAA,MAAK,aAAa,kBAAkB,CAACnB,SAAQmB,OAAM,QAAQ;AACvD,wBAAoBnB,SAAQ,GAAG;AAAA,EACnC,CAAC;AACL;AAEA,SAAS,aAAamB,OAAM;AACxB,EAAAA,MAAK,aAAa,oBAAoB,CAACnB,SAAQmB,OAAM,QAAQ;AACzD,UAAMG,kCAAiC,OAAO;AAC9C,UAAMC,wBAAuB,OAAO;AACpC,aAAS,uBAAuB,KAAK;AACjC,UAAI,OAAO,IAAI,aAAa,OAAO,UAAU,UAAU;AACnD,cAAM,YAAY,IAAI,eAAe,IAAI,YAAY;AACrD,gBAAQ,YAAY,YAAY,MAAM,OAAO,KAAK,UAAU,GAAG;AAAA,MACnE;AACA,aAAO,MAAM,IAAI,SAAS,IAAI,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,IACpE;AACA,UAAMC,cAAa,IAAI;AACvB,UAAM,yBAAyB,CAAC;AAChC,UAAM,4CAA4CxB,QAAOwB,YAAW,6CAA6C,CAAC,MAAM;AACxH,UAAM,gBAAgBA,YAAW,SAAS;AAC1C,UAAM,aAAaA,YAAW,MAAM;AACpC,UAAM,gBAAgB;AACtB,QAAI,mBAAmB,CAAC,MAAM;AAC1B,UAAI,IAAI,kBAAkB,GAAG;AACzB,cAAM,YAAY,KAAK,EAAE;AACzB,YAAI,WAAW;AACX,kBAAQ,MAAM,gCAAgC,qBAAqB,QAAQ,UAAU,UAAU,WAAW,WAAW,EAAE,KAAK,MAAM,WAAW,EAAE,QAAQ,EAAE,KAAK,QAAQ,YAAY,WAAW,qBAAqB,QAAQ,UAAU,QAAQ,MAAS;AAAA,QACzP,OACK;AACD,kBAAQ,MAAM,CAAC;AAAA,QACnB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,qBAAqB,MAAM;AAC3B,aAAO,uBAAuB,QAAQ;AAClC,cAAM,uBAAuB,uBAAuB,MAAM;AAC1D,YAAI;AACA,+BAAqB,KAAK,WAAW,MAAM;AACvC,gBAAI,qBAAqB,eAAe;AACpC,oBAAM,qBAAqB;AAAA,YAC/B;AACA,kBAAM;AAAA,UACV,CAAC;AAAA,QACL,SACO,OAAO;AACV,mCAAyB,KAAK;AAAA,QAClC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,6CAA6CA,YAAW,kCAAkC;AAChG,aAAS,yBAAyB,GAAG;AACjC,UAAI,iBAAiB,CAAC;AACtB,UAAI;AACA,cAAM,UAAUL,MAAK,0CAA0C;AAC/D,YAAI,OAAO,YAAY,YAAY;AAC/B,kBAAQ,KAAK,MAAM,CAAC;AAAA,QACxB;AAAA,MACJ,SACO,KAAK;AAAA,MAAE;AAAA,IAClB;AACA,aAAS,WAAW,OAAO;AACvB,aAAO,SAAS,OAAO,MAAM,SAAS;AAAA,IAC1C;AACA,aAAS,kBAAkB,OAAO;AAC9B,aAAO;AAAA,IACX;AACA,aAAS,iBAAiB,WAAW;AACjC,aAAO,iBAAiB,OAAO,SAAS;AAAA,IAC5C;AACA,UAAM,cAAcK,YAAW,OAAO;AACtC,UAAM,cAAcA,YAAW,OAAO;AACtC,UAAM,gBAAgBA,YAAW,SAAS;AAC1C,UAAM,2BAA2BA,YAAW,oBAAoB;AAChE,UAAM,2BAA2BA,YAAW,oBAAoB;AAChE,UAAM,SAAS;AACf,UAAM,aAAa;AACnB,UAAM,WAAW;AACjB,UAAM,WAAW;AACjB,UAAM,oBAAoB;AAC1B,aAAS,aAAa,SAAS,OAAO;AAClC,aAAO,CAAC,MAAM;AACV,YAAI;AACA,yBAAe,SAAS,OAAO,CAAC;AAAA,QACpC,SACO,KAAK;AACR,yBAAe,SAAS,OAAO,GAAG;AAAA,QACtC;AAAA,MAEJ;AAAA,IACJ;AACA,UAAM,OAAO,WAAY;AACrB,UAAI,YAAY;AAChB,aAAO,SAAS,QAAQ,iBAAiB;AACrC,eAAO,WAAY;AACf,cAAI,WAAW;AACX;AAAA,UACJ;AACA,sBAAY;AACZ,0BAAgB,MAAM,MAAM,SAAS;AAAA,QACzC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa;AACnB,UAAM,4BAA4BA,YAAW,kBAAkB;AAE/D,aAAS,eAAe,SAAS,OAAO,OAAO;AAC3C,YAAM,cAAc,KAAK;AACzB,UAAI,YAAY,OAAO;AACnB,cAAM,IAAI,UAAU,UAAU;AAAA,MAClC;AACA,UAAI,QAAQ,WAAW,MAAM,YAAY;AAErC,YAAI,OAAO;AACX,YAAI;AACA,cAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY;AAC1D,mBAAO,SAAS,MAAM;AAAA,UAC1B;AAAA,QACJ,SACO,KAAK;AACR,sBAAY,MAAM;AACd,2BAAe,SAAS,OAAO,GAAG;AAAA,UACtC,CAAC,EAAE;AACH,iBAAO;AAAA,QACX;AAEA,YAAI,UAAU,YACV,iBAAiB,oBACjB,MAAM,eAAe,WAAW,KAChC,MAAM,eAAe,WAAW,KAChC,MAAM,WAAW,MAAM,YAAY;AACnC,+BAAqB,KAAK;AAC1B,yBAAe,SAAS,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC;AAAA,QAClE,WACS,UAAU,YAAY,OAAO,SAAS,YAAY;AACvD,cAAI;AACA,iBAAK,KAAK,OAAO,YAAY,aAAa,SAAS,KAAK,CAAC,GAAG,YAAY,aAAa,SAAS,KAAK,CAAC,CAAC;AAAA,UACzG,SACO,KAAK;AACR,wBAAY,MAAM;AACd,6BAAe,SAAS,OAAO,GAAG;AAAA,YACtC,CAAC,EAAE;AAAA,UACP;AAAA,QACJ,OACK;AACD,kBAAQ,WAAW,IAAI;AACvB,gBAAM,QAAQ,QAAQ,WAAW;AACjC,kBAAQ,WAAW,IAAI;AACvB,cAAI,QAAQ,aAAa,MAAM,eAAe;AAE1C,gBAAI,UAAU,UAAU;AAGpB,sBAAQ,WAAW,IAAI,QAAQ,wBAAwB;AACvD,sBAAQ,WAAW,IAAI,QAAQ,wBAAwB;AAAA,YAC3D;AAAA,UACJ;AAGA,cAAI,UAAU,YAAY,iBAAiB,OAAO;AAE9C,kBAAM,QAAQL,MAAK,eACfA,MAAK,YAAY,QACjBA,MAAK,YAAY,KAAK,aAAa;AACvC,gBAAI,OAAO;AAEP,cAAAI,sBAAqB,OAAO,2BAA2B;AAAA,gBACnD,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,OAAO;AAAA,cACX,CAAC;AAAA,YACL;AAAA,UACJ;AACA,mBAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AAC/B,oCAAwB,SAAS,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC;AAAA,UACnF;AACA,cAAI,MAAM,UAAU,KAAK,SAAS,UAAU;AACxC,oBAAQ,WAAW,IAAI;AACvB,gBAAI,uBAAuB;AAC3B,gBAAI;AAIA,oBAAM,IAAI,MAAM,4BACZ,uBAAuB,KAAK,KAC3B,SAAS,MAAM,QAAQ,OAAO,MAAM,QAAQ,GAAG;AAAA,YACxD,SACO,KAAK;AACR,qCAAuB;AAAA,YAC3B;AACA,gBAAI,2CAA2C;AAG3C,mCAAqB,gBAAgB;AAAA,YACzC;AACA,iCAAqB,YAAY;AACjC,iCAAqB,UAAU;AAC/B,iCAAqB,OAAOJ,MAAK;AACjC,iCAAqB,OAAOA,MAAK;AACjC,mCAAuB,KAAK,oBAAoB;AAChD,gBAAI,kBAAkB;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AACA,UAAM,4BAA4BK,YAAW,yBAAyB;AACtE,aAAS,qBAAqB,SAAS;AACnC,UAAI,QAAQ,WAAW,MAAM,mBAAmB;AAM5C,YAAI;AACA,gBAAM,UAAUL,MAAK,yBAAyB;AAC9C,cAAI,WAAW,OAAO,YAAY,YAAY;AAC1C,oBAAQ,KAAK,MAAM,EAAE,WAAW,QAAQ,WAAW,GAAG,QAAiB,CAAC;AAAA,UAC5E;AAAA,QACJ,SACO,KAAK;AAAA,QAAE;AACd,gBAAQ,WAAW,IAAI;AACvB,iBAAS,IAAI,GAAG,IAAI,uBAAuB,QAAQ,KAAK;AACpD,cAAI,YAAY,uBAAuB,CAAC,EAAE,SAAS;AAC/C,mCAAuB,OAAO,GAAG,CAAC;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,wBAAwB,SAAS,MAAM,cAAc,aAAa,YAAY;AACnF,2BAAqB,OAAO;AAC5B,YAAM,eAAe,QAAQ,WAAW;AACxC,YAAM,WAAW,eACX,OAAO,gBAAgB,aACnB,cACA,oBACJ,OAAO,eAAe,aAClB,aACA;AACV,WAAK,kBAAkB,QAAQ,MAAM;AACjC,YAAI;AACA,gBAAM,qBAAqB,QAAQ,WAAW;AAC9C,gBAAM,mBAAmB,CAAC,CAAC,gBAAgB,kBAAkB,aAAa,aAAa;AACvF,cAAI,kBAAkB;AAElB,yBAAa,wBAAwB,IAAI;AACzC,yBAAa,wBAAwB,IAAI;AAAA,UAC7C;AAEA,gBAAM,QAAQ,KAAK,IAAI,UAAU,QAAW,oBAAoB,aAAa,oBAAoB,aAAa,oBACxG,CAAC,IACD,CAAC,kBAAkB,CAAC;AAC1B,yBAAe,cAAc,MAAM,KAAK;AAAA,QAC5C,SACO,OAAO;AAEV,yBAAe,cAAc,OAAO,KAAK;AAAA,QAC7C;AAAA,MACJ,GAAG,YAAY;AAAA,IACnB;AACA,UAAM,+BAA+B;AACrC,UAAM,OAAO,WAAY;AAAA,IAAE;AAC3B,UAAM,iBAAiBnB,QAAO;AAAA,IAC9B,MAAM,iBAAiB;AAAA,MACnB,OAAO,WAAW;AACd,eAAO;AAAA,MACX;AAAA,MACA,OAAO,QAAQ,OAAO;AAClB,YAAI,iBAAiB,kBAAkB;AACnC,iBAAO;AAAA,QACX;AACA,eAAO,eAAe,IAAI,KAAK,IAAI,GAAG,UAAU,KAAK;AAAA,MACzD;AAAA,MACA,OAAO,OAAO,OAAO;AACjB,eAAO,eAAe,IAAI,KAAK,IAAI,GAAG,UAAU,KAAK;AAAA,MACzD;AAAA,MACA,OAAO,gBAAgB;AACnB,cAAM,SAAS,CAAC;AAChB,eAAO,UAAU,IAAI,iBAAiB,CAAC,KAAK,QAAQ;AAChD,iBAAO,UAAU;AACjB,iBAAO,SAAS;AAAA,QACpB,CAAC;AACD,eAAO;AAAA,MACX;AAAA,MACA,OAAO,IAAI,QAAQ;AACf,YAAI,CAAC,UAAU,OAAO,OAAO,OAAO,QAAQ,MAAM,YAAY;AAC1D,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC9E;AACA,cAAM,WAAW,CAAC;AAClB,YAAI,QAAQ;AACZ,YAAI;AACA,mBAAS,KAAK,QAAQ;AAClB;AACA,qBAAS,KAAK,iBAAiB,QAAQ,CAAC,CAAC;AAAA,UAC7C;AAAA,QACJ,SACO,KAAK;AACR,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC9E;AACA,YAAI,UAAU,GAAG;AACb,iBAAO,QAAQ,OAAO,IAAI,eAAe,CAAC,GAAG,4BAA4B,CAAC;AAAA,QAC9E;AACA,YAAI,WAAW;AACf,cAAM,SAAS,CAAC;AAChB,eAAO,IAAI,iBAAiB,CAAC,SAAS,WAAW;AAC7C,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,qBAAS,CAAC,EAAE,KAAK,CAAC,MAAM;AACpB,kBAAI,UAAU;AACV;AAAA,cACJ;AACA,yBAAW;AACX,sBAAQ,CAAC;AAAA,YACb,GAAG,CAAC,QAAQ;AACR,qBAAO,KAAK,GAAG;AACf;AACA,kBAAI,UAAU,GAAG;AACb,2BAAW;AACX,uBAAO,IAAI,eAAe,QAAQ,4BAA4B,CAAC;AAAA,cACnE;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,OAAO,KAAK,QAAQ;AAChB,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU,IAAI,KAAK,CAAC,KAAK,QAAQ;AACjC,oBAAU;AACV,mBAAS;AAAA,QACb,CAAC;AACD,iBAAS,UAAU,OAAO;AACtB,kBAAQ,KAAK;AAAA,QACjB;AACA,iBAAS,SAAS,OAAO;AACrB,iBAAO,KAAK;AAAA,QAChB;AACA,iBAAS,SAAS,QAAQ;AACtB,cAAI,CAAC,WAAW,KAAK,GAAG;AACpB,oBAAQ,KAAK,QAAQ,KAAK;AAAA,UAC9B;AACA,gBAAM,KAAK,WAAW,QAAQ;AAAA,QAClC;AACA,eAAO;AAAA,MACX;AAAA,MACA,OAAO,IAAI,QAAQ;AACf,eAAO,iBAAiB,gBAAgB,MAAM;AAAA,MAClD;AAAA,MACA,OAAO,WAAW,QAAQ;AACtB,cAAM,IAAI,QAAQ,KAAK,qBAAqB,mBAAmB,OAAO;AACtE,eAAO,EAAE,gBAAgB,QAAQ;AAAA,UAC7B,cAAc,CAAC,WAAW,EAAE,QAAQ,aAAa,MAAM;AAAA,UACvD,eAAe,CAAC,SAAS,EAAE,QAAQ,YAAY,QAAQ,IAAI;AAAA,QAC/D,CAAC;AAAA,MACL;AAAA,MACA,OAAO,gBAAgB,QAAQ,UAAU;AACrC,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU,IAAI,KAAK,CAAC,KAAK,QAAQ;AACjC,oBAAU;AACV,mBAAS;AAAA,QACb,CAAC;AAED,YAAI,kBAAkB;AACtB,YAAI,aAAa;AACjB,cAAM,iBAAiB,CAAC;AACxB,iBAAS,SAAS,QAAQ;AACtB,cAAI,CAAC,WAAW,KAAK,GAAG;AACpB,oBAAQ,KAAK,QAAQ,KAAK;AAAA,UAC9B;AACA,gBAAM,gBAAgB;AACtB,cAAI;AACA,kBAAM,KAAK,CAACyB,WAAU;AAClB,6BAAe,aAAa,IAAI,WAAW,SAAS,aAAaA,MAAK,IAAIA;AAC1E;AACA,kBAAI,oBAAoB,GAAG;AACvB,wBAAQ,cAAc;AAAA,cAC1B;AAAA,YACJ,GAAG,CAAC,QAAQ;AACR,kBAAI,CAAC,UAAU;AACX,uBAAO,GAAG;AAAA,cACd,OACK;AACD,+BAAe,aAAa,IAAI,SAAS,cAAc,GAAG;AAC1D;AACA,oBAAI,oBAAoB,GAAG;AACvB,0BAAQ,cAAc;AAAA,gBAC1B;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL,SACO,SAAS;AACZ,mBAAO,OAAO;AAAA,UAClB;AACA;AACA;AAAA,QACJ;AAEA,2BAAmB;AACnB,YAAI,oBAAoB,GAAG;AACvB,kBAAQ,cAAc;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AAAA,MACA,YAAY,UAAU;AAClB,cAAM,UAAU;AAChB,YAAI,EAAE,mBAAmB,mBAAmB;AACxC,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QACpD;AACA,gBAAQ,WAAW,IAAI;AACvB,gBAAQ,WAAW,IAAI,CAAC;AACxB,YAAI;AACA,gBAAM,cAAc,KAAK;AACzB,sBACI,SAAS,YAAY,aAAa,SAAS,QAAQ,CAAC,GAAG,YAAY,aAAa,SAAS,QAAQ,CAAC,CAAC;AAAA,QAC3G,SACO,OAAO;AACV,yBAAe,SAAS,OAAO,KAAK;AAAA,QACxC;AAAA,MACJ;AAAA,MACA,KAAK,OAAO,WAAW,IAAI;AACvB,eAAO;AAAA,MACX;AAAA,MACA,KAAK,OAAO,OAAO,IAAI;AACnB,eAAO;AAAA,MACX;AAAA,MACA,KAAK,aAAa,YAAY;AAS1B,YAAI,IAAI,KAAK,cAAc,OAAO,OAAO;AACzC,YAAI,CAAC,KAAK,OAAO,MAAM,YAAY;AAC/B,cAAI,KAAK,eAAe;AAAA,QAC5B;AACA,cAAM,eAAe,IAAI,EAAE,IAAI;AAC/B,cAAM,OAAON,MAAK;AAClB,YAAI,KAAK,WAAW,KAAK,YAAY;AACjC,eAAK,WAAW,EAAE,KAAK,MAAM,cAAc,aAAa,UAAU;AAAA,QACtE,OACK;AACD,kCAAwB,MAAM,MAAM,cAAc,aAAa,UAAU;AAAA,QAC7E;AACA,eAAO;AAAA,MACX;AAAA,MACA,MAAM,YAAY;AACd,eAAO,KAAK,KAAK,MAAM,UAAU;AAAA,MACrC;AAAA,MACA,QAAQ,WAAW;AAEf,YAAI,IAAI,KAAK,cAAc,OAAO,OAAO;AACzC,YAAI,CAAC,KAAK,OAAO,MAAM,YAAY;AAC/B,cAAI;AAAA,QACR;AACA,cAAM,eAAe,IAAI,EAAE,IAAI;AAC/B,qBAAa,aAAa,IAAI;AAC9B,cAAM,OAAOA,MAAK;AAClB,YAAI,KAAK,WAAW,KAAK,YAAY;AACjC,eAAK,WAAW,EAAE,KAAK,MAAM,cAAc,WAAW,SAAS;AAAA,QACnE,OACK;AACD,kCAAwB,MAAM,MAAM,cAAc,WAAW,SAAS;AAAA,QAC1E;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAGA,qBAAiB,SAAS,IAAI,iBAAiB;AAC/C,qBAAiB,QAAQ,IAAI,iBAAiB;AAC9C,qBAAiB,MAAM,IAAI,iBAAiB;AAC5C,qBAAiB,KAAK,IAAI,iBAAiB;AAC3C,UAAM,gBAAiBnB,QAAO,aAAa,IAAIA,QAAO,SAAS;AAC/D,IAAAA,QAAO,SAAS,IAAI;AACpB,UAAM,oBAAoBwB,YAAW,aAAa;AAClD,aAAS,UAAU,MAAM;AACrB,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAOF,gCAA+B,OAAO,MAAM;AACzD,UAAI,SAAS,KAAK,aAAa,SAAS,CAAC,KAAK,eAAe;AAGzD;AAAA,MACJ;AACA,YAAM,eAAe,MAAM;AAE3B,YAAM,UAAU,IAAI;AACpB,WAAK,UAAU,OAAO,SAAU,WAAW,UAAU;AACjD,cAAM,UAAU,IAAI,iBAAiB,CAAC,SAAS,WAAW;AACtD,uBAAa,KAAK,MAAM,SAAS,MAAM;AAAA,QAC3C,CAAC;AACD,eAAO,QAAQ,KAAK,WAAW,QAAQ;AAAA,MAC3C;AACA,WAAK,iBAAiB,IAAI;AAAA,IAC9B;AACA,QAAI,YAAY;AAChB,aAAS,QAAQ,IAAI;AACjB,aAAO,SAAUvB,OAAM,MAAM;AACzB,YAAI,gBAAgB,GAAG,MAAMA,OAAM,IAAI;AACvC,YAAI,yBAAyB,kBAAkB;AAC3C,iBAAO;AAAA,QACX;AACA,YAAI,OAAO,cAAc;AACzB,YAAI,CAAC,KAAK,iBAAiB,GAAG;AAC1B,oBAAU,IAAI;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,eAAe;AACf,gBAAU,aAAa;AACvB,kBAAYC,SAAQ,SAAS,CAAC,aAAa,QAAQ,QAAQ,CAAC;AAAA,IAChE;AAEA,YAAQmB,MAAK,WAAW,uBAAuB,CAAC,IAAI;AACpD,WAAO;AAAA,EACX,CAAC;AACL;AAEA,SAAS,cAAcA,OAAM;AAGzB,EAAAA,MAAK,aAAa,YAAY,CAACnB,YAAW;AAEtC,UAAM,2BAA2B,SAAS,UAAU;AACpD,UAAM,2BAA2B,WAAW,kBAAkB;AAC9D,UAAM,iBAAiB,WAAW,SAAS;AAC3C,UAAM,eAAe,WAAW,OAAO;AACvC,UAAM,sBAAsB,SAAS,WAAW;AAC5C,UAAI,OAAO,SAAS,YAAY;AAC5B,cAAM,mBAAmB,KAAK,wBAAwB;AACtD,YAAI,kBAAkB;AAClB,cAAI,OAAO,qBAAqB,YAAY;AACxC,mBAAO,yBAAyB,KAAK,gBAAgB;AAAA,UACzD,OACK;AACD,mBAAO,OAAO,UAAU,SAAS,KAAK,gBAAgB;AAAA,UAC1D;AAAA,QACJ;AACA,YAAI,SAAS,SAAS;AAClB,gBAAM,gBAAgBA,QAAO,cAAc;AAC3C,cAAI,eAAe;AACf,mBAAO,yBAAyB,KAAK,aAAa;AAAA,UACtD;AAAA,QACJ;AACA,YAAI,SAAS,OAAO;AAChB,gBAAM,cAAcA,QAAO,YAAY;AACvC,cAAI,aAAa;AACb,mBAAO,yBAAyB,KAAK,WAAW;AAAA,UACpD;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,yBAAyB,KAAK,IAAI;AAAA,IAC7C;AACA,wBAAoB,wBAAwB,IAAI;AAChD,aAAS,UAAU,WAAW;AAE9B,UAAM,yBAAyB,OAAO,UAAU;AAChD,UAAM,2BAA2B;AACjC,WAAO,UAAU,WAAW,WAAY;AACpC,UAAI,OAAO,YAAY,cAAc,gBAAgB,SAAS;AAC1D,eAAO;AAAA,MACX;AACA,aAAO,uBAAuB,KAAK,IAAI;AAAA,IAC3C;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,eAAe,KAAK,QAAQ,YAAY,QAAQ,WAAW;AAChE,QAAM,SAAS,KAAK,WAAW,MAAM;AACrC,MAAI,OAAO,MAAM,GAAG;AAChB;AAAA,EACJ;AACA,QAAM,iBAAkB,OAAO,MAAM,IAAI,OAAO,MAAM;AACtD,SAAO,MAAM,IAAI,SAAU,MAAM,MAAM,SAAS;AAC5C,QAAI,QAAQ,KAAK,WAAW;AACxB,gBAAU,QAAQ,SAAU,UAAU;AAClC,cAAM,SAAS,GAAG,UAAU,IAAI,MAAM,OAAO;AAC7C,cAAM,YAAY,KAAK;AASvB,YAAI;AACA,cAAI,UAAU,eAAe,QAAQ,GAAG;AACpC,kBAAM,aAAa,IAAI,+BAA+B,WAAW,QAAQ;AACzE,gBAAI,cAAc,WAAW,OAAO;AAChC,yBAAW,QAAQ,IAAI,oBAAoB,WAAW,OAAO,MAAM;AACnE,kBAAI,kBAAkB,KAAK,WAAW,UAAU,UAAU;AAAA,YAC9D,WACS,UAAU,QAAQ,GAAG;AAC1B,wBAAU,QAAQ,IAAI,IAAI,oBAAoB,UAAU,QAAQ,GAAG,MAAM;AAAA,YAC7E;AAAA,UACJ,WACS,UAAU,QAAQ,GAAG;AAC1B,sBAAU,QAAQ,IAAI,IAAI,oBAAoB,UAAU,QAAQ,GAAG,MAAM;AAAA,UAC7E;AAAA,QACJ,QACM;AAAA,QAGN;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO,eAAe,KAAK,QAAQ,MAAM,MAAM,OAAO;AAAA,EAC1D;AACA,MAAI,sBAAsB,OAAO,MAAM,GAAG,cAAc;AAC5D;AAEA,SAAS,UAAUmB,OAAM;AACrB,EAAAA,MAAK,aAAa,QAAQ,CAACnB,SAAQmB,OAAM,QAAQ;AAG7C,UAAM,aAAa,gBAAgBnB,OAAM;AACzC,QAAI,oBAAoB;AACxB,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,iBAAiB;AAMrB,UAAM,6BAA6BmB,MAAK,WAAW,qBAAqB;AACxE,UAAM,0BAA0BA,MAAK,WAAW,kBAAkB;AAClE,QAAInB,QAAO,uBAAuB,GAAG;AACjC,MAAAA,QAAO,0BAA0B,IAAIA,QAAO,uBAAuB;AAAA,IACvE;AACA,QAAIA,QAAO,0BAA0B,GAAG;AACpC,MAAAmB,MAAK,0BAA0B,IAAIA,MAAK,uBAAuB,IAC3DnB,QAAO,0BAA0B;AAAA,IACzC;AACA,QAAI,sBAAsB;AAC1B,QAAI,mBAAmB;AACvB,QAAI,aAAa;AACjB,QAAI,uBAAuB;AAC3B,QAAI,iCAAiC;AACrC,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,sBAAsB;AAC1B,QAAI,mBAAmB;AACvB,QAAI,wBAAwB;AAC5B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,iBAAiB;AACrB,QAAI,mBAAmB,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,YAAYmB,OAAM;AACvB,eAAaA,KAAI;AACjB,gBAAcA,KAAI;AAClB,YAAUA,KAAI;AAClB;AAEA,IAAM,SAAS,SAAS;AACxB,YAAY,MAAM;AAClB,aAAa,MAAM;", "names": ["self", "global", "delegate", "prop", "_global", "event", "patchOptions", "<PERSON><PERSON><PERSON><PERSON>", "window", "handle", "handleId", "isPeriodic", "isRefreshable", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "internalWindow", "Zone", "name", "loadTasks", "ObjectGetOwnPropertyDescriptor", "ObjectDefineProperty", "__symbol__", "value"]}