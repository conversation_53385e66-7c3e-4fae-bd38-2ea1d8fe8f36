var tg=Object.defineProperty,ng=Object.defineProperties;var rg=Object.getOwnPropertyDescriptors;var Vu=Object.getOwnPropertySymbols;var og=Object.prototype.hasOwnProperty,ig=Object.prototype.propertyIsEnumerable;var Uu=(e,t,n)=>t in e?tg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||={})og.call(t,n)&&Uu(e,n,t[n]);if(Vu)for(var n of Vu(t))ig.call(t,n)&&Uu(e,n,t[n]);return e},j=(e,t)=>ng(e,rg(t));var ds;function fs(){return ds}function Ve(e){let t=ds;return ds=e,t}var sg=Symbol("NotFound"),Wr=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function Qt(e){return e===sg||e?.name==="\u0275NotFound"}function $u(e,t){return Object.is(e,t)}var z=null,qr=!1,ps=1;var Zr=Symbol("SIGNAL");function M(e){let t=z;return z=e,t}function gs(){return z}var Yr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function ms(e){if(qr)throw new Error("");if(z===null)return;z.consumerOnSignalRead(e);let t=z.nextProducerIndex++;if(Xr(z),t<z.producerNode.length&&z.producerNode[t]!==e&&Ln(z)){let n=z.producerNode[t];Jr(n,z.producerIndexOfThis[t])}z.producerNode[t]!==e&&(z.producerNode[t]=e,z.producerIndexOfThis[t]=Ln(z)?Wu(e,z,t):0),z.producerLastReadVersion[t]=e.version}function Hu(){ps++}function zu(e){if(!(Ln(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ps)){if(!e.producerMustRecompute(e)&&!Kr(e)){hs(e);return}e.producerRecomputeValue(e),hs(e)}}function ys(e){if(e.liveConsumerNode===void 0)return;let t=qr;qr=!0;try{for(let n of e.liveConsumerNode)n.dirty||ag(n)}finally{qr=t}}function Gu(){return z?.consumerAllowSignalWrites!==!1}function ag(e){e.dirty=!0,ys(e),e.consumerMarkedDirty?.(e)}function hs(e){e.dirty=!1,e.lastCleanEpoch=ps}function Qr(e){return e&&(e.nextProducerIndex=0),M(e)}function vs(e,t){if(M(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Ln(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Jr(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Kr(e){Xr(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(zu(n),r!==n.version))return!0}return!1}function Ds(e){if(Xr(e),Ln(e))for(let t=0;t<e.producerNode.length;t++)Jr(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Wu(e,t,n){if(qu(e),e.liveConsumerNode.length===0&&Zu(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Wu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Jr(e,t){if(qu(e),e.liveConsumerNode.length===1&&Zu(e))for(let r=0;r<e.producerNode.length;r++)Jr(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Xr(o),o.producerIndexOfThis[r]=t}}function Ln(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Xr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function qu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Zu(e){return e.producerNode!==void 0}function cg(){throw new Error}var Yu=cg;function ug(e){Yu(e)}function Es(e){Yu=e}var lg=null;function Is(e,t){Gu()||ug(e),e.equal(e.value,t)||(e.value=t,dg(e))}var ws=j(m({},Yr),{equal:$u,value:void 0,kind:"signal"});function dg(e){e.version++,Hu(),ys(e),lg?.(e)}function w(e){return typeof e=="function"}function Kt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var eo=Kt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function jn(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var V=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(w(r))try{r()}catch(i){t=i instanceof eo?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Qu(i)}catch(s){t=t??[],s instanceof eo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new eo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Qu(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&jn(n,t)}remove(t){let{_finalizers:n}=this;n&&jn(n,t),t instanceof e&&t._removeParent(this)}};V.EMPTY=(()=>{let e=new V;return e.closed=!0,e})();var Cs=V.EMPTY;function to(e){return e instanceof V||e&&"closed"in e&&w(e.remove)&&w(e.add)&&w(e.unsubscribe)}function Qu(e){w(e)?e():e.unsubscribe()}var be={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Jt={setTimeout(e,t,...n){let{delegate:r}=Jt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Jt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function no(e){Jt.setTimeout(()=>{let{onUnhandledError:t}=be;if(t)t(e);else throw e})}function Bn(){}var Ku=bs("C",void 0,void 0);function Ju(e){return bs("E",void 0,e)}function Xu(e){return bs("N",e,void 0)}function bs(e,t,n){return{kind:e,value:t,error:n}}var vt=null;function Xt(e){if(be.useDeprecatedSynchronousErrorHandling){let t=!vt;if(t&&(vt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=vt;if(vt=null,n)throw r}}else e()}function el(e){be.useDeprecatedSynchronousErrorHandling&&vt&&(vt.errorThrown=!0,vt.error=e)}var Dt=class extends V{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,to(t)&&t.add(this)):this.destination=pg}static create(t,n,r){return new en(t,n,r)}next(t){this.isStopped?_s(Xu(t),this):this._next(t)}error(t){this.isStopped?_s(Ju(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?_s(Ku,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},fg=Function.prototype.bind;function Ts(e,t){return fg.call(e,t)}var Ss=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ro(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ro(r)}else ro(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ro(n)}}},en=class extends Dt{constructor(t,n,r){super();let o;if(w(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&be.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Ts(t.next,i),error:t.error&&Ts(t.error,i),complete:t.complete&&Ts(t.complete,i)}):o=t}this.destination=new Ss(o)}};function ro(e){be.useDeprecatedSynchronousErrorHandling?el(e):no(e)}function hg(e){throw e}function _s(e,t){let{onStoppedNotification:n}=be;n&&Jt.setTimeout(()=>n(e,t))}var pg={closed:!0,next:Bn,error:hg,complete:Bn};var tn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ue(e){return e}function Ms(...e){return Ns(e)}function Ns(e){return e.length===0?ue:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var R=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=mg(n)?n:new en(n,r,o);return Xt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=tl(r),new r((o,i)=>{let s=new en({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[tn](){return this}pipe(...n){return Ns(n)(this)}toPromise(n){return n=tl(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function tl(e){var t;return(t=e??be.Promise)!==null&&t!==void 0?t:Promise}function gg(e){return e&&w(e.next)&&w(e.error)&&w(e.complete)}function mg(e){return e&&e instanceof Dt||gg(e)&&to(e)}function Rs(e){return w(e?.lift)}function O(e){return t=>{if(Rs(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function A(e,t,n,r,o){return new As(e,t,n,r,o)}var As=class extends Dt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function nn(){return O((e,t)=>{let n=null;e._refCount++;let r=A(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var rn=class extends R{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Rs(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new V;let n=this.getSubject();t.add(this.source.subscribe(A(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=V.EMPTY)}return t}refCount(){return nn()(this)}};var nl=Kt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var X=(()=>{class e extends R{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new oo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new nl}next(n){Xt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Xt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Xt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Cs:(this.currentObservers=null,i.push(n),new V(()=>{this.currentObservers=null,jn(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new R;return n.source=this,n}}return e.create=(t,n)=>new oo(t,n),e})(),oo=class extends X{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Cs}};var ee=class extends X{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var oe=new R(e=>e.complete());function rl(e){return e&&w(e.schedule)}function ol(e){return e[e.length-1]}function io(e){return w(ol(e))?e.pop():void 0}function et(e){return rl(ol(e))?e.pop():void 0}function sl(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function il(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Et(e){return this instanceof Et?(this.v=e,this):new Et(e)}function al(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(I){return new Promise(function(P,x){i.push([f,I,P,x])>1||c(f,I)})},g&&(o[f]=g(o[f])))}function c(f,g){try{u(r[f](g))}catch(I){h(i[0][3],I)}}function u(f){f.value instanceof Et?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function cl(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof il=="function"?il(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var so=e=>e&&typeof e.length=="number"&&typeof e!="function";function ao(e){return w(e?.then)}function co(e){return w(e[tn])}function uo(e){return Symbol.asyncIterator&&w(e?.[Symbol.asyncIterator])}function lo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function yg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var fo=yg();function ho(e){return w(e?.[fo])}function po(e){return al(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Et(n.read());if(o)return yield Et(void 0);yield yield Et(r)}}finally{n.releaseLock()}})}function go(e){return w(e?.getReader)}function U(e){if(e instanceof R)return e;if(e!=null){if(co(e))return vg(e);if(so(e))return Dg(e);if(ao(e))return Eg(e);if(uo(e))return ul(e);if(ho(e))return Ig(e);if(go(e))return wg(e)}throw lo(e)}function vg(e){return new R(t=>{let n=e[tn]();if(w(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Dg(e){return new R(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Eg(e){return new R(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,no)})}function Ig(e){return new R(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function ul(e){return new R(t=>{Cg(e,t).catch(n=>t.error(n))})}function wg(e){return ul(po(e))}function Cg(e,t){var n,r,o,i;return sl(this,void 0,void 0,function*(){try{for(n=cl(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ie(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function mo(e,t=0){return O((n,r)=>{n.subscribe(A(r,o=>ie(r,e,()=>r.next(o),t),()=>ie(r,e,()=>r.complete(),t),o=>ie(r,e,()=>r.error(o),t)))})}function yo(e,t=0){return O((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function ll(e,t){return U(e).pipe(yo(t),mo(t))}function dl(e,t){return U(e).pipe(yo(t),mo(t))}function fl(e,t){return new R(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function hl(e,t){return new R(n=>{let r;return ie(n,t,()=>{r=e[fo](),ie(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>w(r?.return)&&r.return()})}function vo(e,t){if(!e)throw new Error("Iterable cannot be null");return new R(n=>{ie(n,t,()=>{let r=e[Symbol.asyncIterator]();ie(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function pl(e,t){return vo(po(e),t)}function gl(e,t){if(e!=null){if(co(e))return ll(e,t);if(so(e))return fl(e,t);if(ao(e))return dl(e,t);if(uo(e))return vo(e,t);if(ho(e))return hl(e,t);if(go(e))return pl(e,t)}throw lo(e)}function $(e,t){return t?gl(e,t):U(e)}function E(...e){let t=et(e);return $(e,t)}function on(e,t){let n=w(e)?e:()=>e,r=o=>o.error(n());return new R(t?o=>t.schedule(r,0,o):r)}function xs(e){return!!e&&(e instanceof R||w(e.lift)&&w(e.subscribe))}var Ue=Kt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function _(e,t){return O((n,r)=>{let o=0;n.subscribe(A(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:bg}=Array;function Tg(e,t){return bg(t)?e(...t):e(t)}function Do(e){return _(t=>Tg(e,t))}var{isArray:_g}=Array,{getPrototypeOf:Sg,prototype:Mg,keys:Ng}=Object;function Eo(e){if(e.length===1){let t=e[0];if(_g(t))return{args:t,keys:null};if(Rg(t)){let n=Ng(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Rg(e){return e&&typeof e=="object"&&Sg(e)===Mg}function Io(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function wo(...e){let t=et(e),n=io(e),{args:r,keys:o}=Eo(e);if(r.length===0)return $([],t);let i=new R(Ag(r,t,o?s=>Io(o,s):ue));return n?i.pipe(Do(n)):i}function Ag(e,t,n=ue){return r=>{ml(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ml(t,()=>{let u=$(e[c],t),l=!1;u.subscribe(A(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ml(e,t,n){e?ie(n,e,t):t()}function yl(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=I=>u<r?g(I):c.push(I),g=I=>{i&&t.next(I),u++;let P=!1;U(n(I,l++)).subscribe(A(t,x=>{o?.(x),i?f(x):t.next(x)},()=>{P=!0},void 0,()=>{if(P)try{for(u--;c.length&&u<r;){let x=c.shift();s?ie(t,s,()=>g(x)):g(x)}h()}catch(x){t.error(x)}}))};return e.subscribe(A(t,f,()=>{d=!0,h()})),()=>{a?.()}}function G(e,t,n=1/0){return w(t)?G((r,o)=>_((i,s)=>t(r,i,o,s))(U(e(r,o))),n):(typeof t=="number"&&(n=t),O((r,o)=>yl(r,o,e,n)))}function vl(e=1/0){return G(ue,e)}function Dl(){return vl(1)}function sn(...e){return Dl()($(e,et(e)))}function Vn(e){return new R(t=>{U(e()).subscribe(t)})}function xg(...e){let t=io(e),{args:n,keys:r}=Eo(e),o=new R(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;U(n[l]).subscribe(A(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Io(r,a):a),i.complete())}))}});return t?o.pipe(Do(t)):o}function se(e,t){return O((n,r)=>{let o=0;n.subscribe(A(r,i=>e.call(t,i,o++)&&r.next(i)))})}function tt(e){return O((t,n)=>{let r=null,o=!1,i;r=t.subscribe(A(n,void 0,void 0,s=>{i=U(e(s,tt(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function El(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(A(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function nt(e,t){return w(t)?G(e,t,1):G(e,1)}function rt(e){return O((t,n)=>{let r=!1;t.subscribe(A(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function $e(e){return e<=0?()=>oe:O((t,n)=>{let r=0;t.subscribe(A(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Co(e=Og){return O((t,n)=>{let r=!1;t.subscribe(A(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Og(){return new Ue}function an(e){return O((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function He(e,t){let n=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):ue,$e(1),n?rt(t):Co(()=>new Ue))}function cn(e){return e<=0?()=>oe:O((t,n)=>{let r=[];t.subscribe(A(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Os(e,t){let n=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):ue,cn(1),n?rt(t):Co(()=>new Ue))}function Ps(e,t){return O(El(e,t,arguments.length>=2,!0))}function ks(...e){let t=et(e);return O((n,r)=>{(t?sn(e,n,t):sn(e,n)).subscribe(r)})}function le(e,t){return O((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(A(r,c=>{o?.unsubscribe();let u=0,l=i++;U(e(c,l)).subscribe(o=A(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function bo(e){return O((t,n)=>{U(e).subscribe(A(n,()=>n.complete(),Bn)),!n.closed&&t.subscribe(n)})}function Z(e,t,n){let r=w(e)||t||n?{next:e,error:t,complete:n}:e;return r?O((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(A(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ue}var Ro="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(t,n){super(Ao(t,n)),this.code=t}};function kg(e){return`NG0${Math.abs(e)}`}function Ao(e,t){return`${kg(e)}${t?": "+t:""}`}function B(e){for(let t in e)if(e[t]===B)return t;throw Error("")}function fe(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(fe).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Zs(e,t){return e?t?`${e} ${t}`:e:t||""}var Fg=B({__forward_ref__:B});function xo(e){return e.__forward_ref__=xo,e.toString=function(){return fe(this())},e}function ae(e){return Ys(e)?e():e}function Ys(e){return typeof e=="function"&&e.hasOwnProperty(Fg)&&e.__forward_ref__===xo}function bl(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function _t(e){return{providers:e.providers||[],imports:e.imports||[]}}function zn(e){return Lg(e,Oo)}function Qs(e){return zn(e)!==null}function Lg(e,t){return e.hasOwnProperty(t)&&e[t]||null}function jg(e){let t=e?.[Oo]??null;return t||null}function Ls(e){return e&&e.hasOwnProperty(_o)?e[_o]:null}var Oo=B({\u0275prov:B}),_o=B({\u0275inj:B}),v=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=D({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Ks(e){return e&&!!e.\u0275providers}var Js=B({\u0275cmp:B}),Xs=B({\u0275dir:B}),ea=B({\u0275pipe:B}),ta=B({\u0275mod:B}),$n=B({\u0275fac:B}),St=B({__NG_ELEMENT_ID__:B}),Il=B({__NG_ENV_ID__:B});function ln(e){return typeof e=="string"?e:e==null?"":String(e)}function Tl(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():ln(e)}function na(e,t){throw new y(-200,e)}function Po(e,t){throw new y(-201,!1)}var js;function _l(){return js}function de(e){let t=js;return js=e,t}function ra(e,t,n){let r=zn(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;Po(e,"Injector")}var Bg={},It=Bg,Bs="__NG_DI_FLAG__",Vs=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=wt(n)||0;try{return this.injector.get(t,r&8?null:It,r)}catch(o){if(Qt(o))return o;throw o}}},So="ngTempTokenPath",Vg="ngTokenPath",Ug=/\n/gm,$g="\u0275",wl="__source";function Hg(e,t=0){let n=fs();if(n===void 0)throw new y(-203,!1);if(n===null)return ra(e,void 0,t);{let r=zg(t),o=n.retrieve(e,r);if(Qt(o)){if(r.optional)return null;throw o}return o}}function C(e,t=0){return(_l()||Hg)(ae(e),t)}function p(e,t){return C(e,wt(t))}function wt(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function zg(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Us(e){let t=[];for(let n=0;n<e.length;n++){let r=ae(e[n]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=Gg(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function oa(e,t){return e[Bs]=t,e.prototype[Bs]=t,e}function Gg(e){return e[Bs]}function Wg(e,t,n,r){let o=e[So];throw t[wl]&&o.unshift(t[wl]),e.message=qg(`
`+e.message,o,n,r),e[Vg]=o,e[So]=null,e}function qg(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==$g?e.slice(2):e;let o=fe(t);if(Array.isArray(t))o=t.map(fe).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):fe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Ug,`
  `)}`}function Ct(e,t){let n=e.hasOwnProperty($n);return n?e[$n]:null}function ko(e,t){e.forEach(n=>Array.isArray(n)?ko(n,t):t(n))}function ia(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Gn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}var Mt={},Te=[],Ge=new v(""),sa=new v("",-1),aa=new v(""),Hn=class{get(t,n=It){if(n===It)throw new Wr(`NullInjectorError: No provider for ${fe(t)}!`);return n}};function ca(e){return e[ta]||null}function it(e){return e[Js]||null}function ua(e){return e[Xs]||null}function Sl(e){return e[ea]||null}function st(e){return{\u0275providers:e}}function Ml(e){return st([{provide:Ge,multi:!0,useValue:e}])}function Nl(...e){return{\u0275providers:la(!0,e),\u0275fromNgModule:!0}}function la(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ko(t,s=>{let a=s;Mo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Rl(o,i),n}function Rl(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];da(o,i=>{t(i,r)})}}function Mo(e,t,n,r){if(e=ae(e),!e)return!1;let o=null,i=Ls(e),s=!i&&it(e);if(!i&&!s){let c=e.ngModule;if(i=Ls(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Mo(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{ko(i.imports,l=>{Mo(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Rl(u,t)}if(!a){let u=Ct(o)||(()=>new o);t({provide:o,useFactory:u,deps:Te},o),t({provide:aa,useValue:o,multi:!0},o),t({provide:Ge,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;da(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function da(e,t){for(let n of e)Ks(n)&&(n=n.\u0275providers),Array.isArray(n)?da(n,t):t(n)}var Zg=B({provide:String,useValue:B});function Al(e){return e!==null&&typeof e=="object"&&Zg in e}function Yg(e){return!!(e&&e.useExisting)}function Qg(e){return!!(e&&e.useFactory)}function No(e){return typeof e=="function"}var Wn=new v(""),To={},Cl={},Fs;function qn(){return Fs===void 0&&(Fs=new Hn),Fs}var Y=class{},bt=class extends Y{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Hs(t,s=>this.processProvider(s)),this.records.set(sa,un(void 0,this)),o.has("environment")&&this.records.set(Y,un(void 0,this));let i=this.records.get(Wn);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(aa,Te,{self:!0}))}retrieve(t,n){let r=wt(n)||0;try{return this.get(t,It,r)}catch(o){if(Qt(o))return o;throw o}}destroy(){Un(this),this._destroyed=!0;let t=M(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),M(t)}}onDestroy(t){return Un(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Un(this);let n=Ve(this),r=de(void 0),o;try{return t()}finally{Ve(n),de(r)}}get(t,n=It,r){if(Un(this),t.hasOwnProperty(Il))return t[Il](this);let o=wt(r),i,s=Ve(this),a=de(void 0);try{if(!(o&4)){let u=this.records.get(t);if(u===void 0){let l=tm(t)&&zn(t);l&&this.injectableDefInScope(l)?u=un($s(t),To):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let c=o&2?qn():this.parent;return n=o&8&&n===It?null:n,c.get(t,n)}catch(c){if(Qt(c)){if((c[So]=c[So]||[]).unshift(fe(t)),s)throw c;return Wg(c,t,"R3InjectorError",this.source)}else throw c}finally{de(a),Ve(s)}}resolveInjectorInitializers(){let t=M(null),n=Ve(this),r=de(void 0),o;try{let i=this.get(Ge,Te,{self:!0});for(let s of i)s()}finally{Ve(n),de(r),M(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(fe(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ae(t);let n=No(t)?t:ae(t&&t.provide),r=Jg(t);if(!No(t)&&t.multi===!0){let o=this.records.get(n);o||(o=un(void 0,To,!0),o.factory=()=>Us(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=M(null);try{return n.value===Cl?na(fe(t)):n.value===To&&(n.value=Cl,n.value=n.factory()),typeof n.value=="object"&&n.value&&em(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{M(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ae(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function $s(e){let t=zn(e),n=t!==null?t.factory:Ct(e);if(n!==null)return n;if(e instanceof v)throw new y(204,!1);if(e instanceof Function)return Kg(e);throw new y(204,!1)}function Kg(e){if(e.length>0)throw new y(204,!1);let n=jg(e);return n!==null?()=>n.factory(e):()=>new e}function Jg(e){if(Al(e))return un(void 0,e.useValue);{let t=xl(e);return un(t,To)}}function xl(e,t,n){let r;if(No(e)){let o=ae(e);return Ct(o)||$s(o)}else if(Al(e))r=()=>ae(e.useValue);else if(Qg(e))r=()=>e.useFactory(...Us(e.deps||[]));else if(Yg(e))r=()=>C(ae(e.useExisting));else{let o=ae(e&&(e.useClass||e.provide));if(Xg(e))r=()=>new o(...Us(e.deps));else return Ct(o)||$s(o)}return r}function Un(e){if(e.destroyed)throw new y(205,!1)}function un(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Xg(e){return!!e.deps}function em(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function tm(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Hs(e,t){for(let n of e)Array.isArray(n)?Hs(n,t):n&&Ks(n)?Hs(n.\u0275providers,t):t(n)}function ce(e,t){let n;e instanceof bt?(Un(e),n=e):n=new Vs(e);let r,o=Ve(n),i=de(void 0);try{return t()}finally{Ve(o),de(i)}}function Ol(){return _l()!==void 0||fs()!=null}var _e=0,S=1,b=2,Q=3,ve=4,De=5,dn=6,fn=7,W=8,Nt=9,xe=10,K=11,hn=12,fa=13,Rt=14,he=15,At=16,xt=17,Ot=18,Zn=19,ha=20,ze=21,Fo=22,Yn=23,pe=24,Lo=25,Ee=26,Pl=1,pa=6,at=7,Qn=8,Kn=9,te=10;function Oe(e){return Array.isArray(e)&&typeof e[Pl]=="object"}function Se(e){return Array.isArray(e)&&e[Pl]===!0}function ga(e){return(e.flags&4)!==0}function ct(e){return e.componentOffset>-1}function jo(e){return(e.flags&1)===1}function Pt(e){return!!e.template}function pn(e){return(e[b]&512)!==0}function kt(e){return(e[b]&256)===256}var ma="svg",kl="math";function Ie(e){for(;Array.isArray(e);)e=e[_e];return e}function Fl(e,t){return Ie(t[e])}function Pe(e,t){return Ie(t[e.index])}function Jn(e,t){return e.data[t]}function we(e,t){let n=t[e];return Oe(n)?n:n[_e]}function Bo(e){return(e[b]&128)===128}function Ll(e){return Se(e[Q])}function Ft(e,t){return t==null?null:e[t]}function ya(e){e[xt]=0}function va(e){e[b]&1024||(e[b]|=1024,Bo(e)&&er(e))}function jl(e,t){for(;e>0;)t=t[Rt],e--;return t}function Xn(e){return!!(e[b]&9216||e[pe]?.dirty)}function Vo(e){e[xe].changeDetectionScheduler?.notify(8),e[b]&64&&(e[b]|=1024),Xn(e)&&er(e)}function er(e){e[xe].changeDetectionScheduler?.notify(0);let t=ot(e);for(;t!==null&&!(t[b]&8192||(t[b]|=8192,!Bo(t)));)t=ot(t)}function Da(e,t){if(kt(e))throw new y(911,!1);e[ze]===null&&(e[ze]=[]),e[ze].push(t)}function Bl(e,t){if(e[ze]===null)return;let n=e[ze].indexOf(t);n!==-1&&e[ze].splice(n,1)}function ot(e){let t=e[Q];return Se(t)?t[Q]:t}function Vl(e){return e[fn]??=[]}function Ul(e){return e.cleanup??=[]}var N={lFrame:rd(null),bindingsEnabled:!0,skipHydrationRootTNode:null},tr=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(tr||{}),nm=0,zs=!1;function $l(){return N.lFrame.elementDepthCount}function Hl(){N.lFrame.elementDepthCount++}function zl(){N.lFrame.elementDepthCount--}function Ea(){return N.bindingsEnabled}function Gl(){return N.skipHydrationRootTNode!==null}function Wl(e){return N.skipHydrationRootTNode===e}function ql(){N.skipHydrationRootTNode=null}function L(){return N.lFrame.lView}function We(){return N.lFrame.tView}function Ce(){let e=Ia();for(;e!==null&&e.type===64;)e=e.parent;return e}function Ia(){return N.lFrame.currentTNode}function Zl(){let e=N.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function gn(e,t){let n=N.lFrame;n.currentTNode=e,n.isParent=t}function wa(){return N.lFrame.isParent}function Yl(){N.lFrame.isParent=!1}function Ca(e){bl("Must never be called in production mode"),nm=e}function ba(){return zs}function Ta(e){let t=zs;return zs=e,t}function _a(){let e=N.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Ql(){return N.lFrame.bindingIndex}function Kl(e){return N.lFrame.bindingIndex=e}function Uo(){return N.lFrame.bindingIndex++}function Jl(e){let t=N.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Xl(){return N.lFrame.inI18n}function ed(e,t){let n=N.lFrame;n.bindingIndex=n.bindingRootIndex=e,$o(t)}function td(){return N.lFrame.currentDirectiveIndex}function $o(e){N.lFrame.currentDirectiveIndex=e}function Sa(e){N.lFrame.currentQueryIndex=e}function rm(e){let t=e[S];return t.type===2?t.declTNode:t.type===1?e[De]:null}function Ma(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=rm(i),o===null||(i=i[Rt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=N.lFrame=nd();return r.currentTNode=t,r.lView=e,!0}function Ho(e){let t=nd(),n=e[S];N.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function nd(){let e=N.lFrame,t=e===null?null:e.child;return t===null?rd(e):t}function rd(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function od(){let e=N.lFrame;return N.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Na=od;function zo(){let e=od();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function id(e){return(N.lFrame.contextLView=jl(e,N.lFrame.contextLView))[W]}function mn(){return N.lFrame.selectedIndex}function ut(e){N.lFrame.selectedIndex=e}function sd(){let e=N.lFrame;return Jn(e.tView,e.selectedIndex)}function ad(){N.lFrame.currentNamespace=ma}function cd(){om()}function om(){N.lFrame.currentNamespace=null}function ud(){return N.lFrame.currentNamespace}var ld=!0;function Go(){return ld}function Wo(e){ld=e}function Gs(e,t=null,n=null,r){let o=Ra(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ra(e,t=null,n=null,r,o=new Set){let i=[n||Te,Nl(e)];return r=r||(typeof e=="object"?void 0:fe(e)),new bt(i,t||qn(),r||null,o)}var Re=class e{static THROW_IF_NOT_FOUND=It;static NULL=new Hn;static create(t,n){if(Array.isArray(t))return Gs({name:""},n,t,"");{let r=t.name??"";return Gs({name:r},t.parent,t.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>C(sa)});static __NG_ELEMENT_ID__=-1},q=new v(""),lt=(()=>{class e{static __NG_ELEMENT_ID__=im;static __NG_ENV_ID__=n=>n}return e})(),Ws=class extends lt{_lView;constructor(t){super(),this._lView=t}get destroyed(){return kt(this._lView)}onDestroy(t){let n=this._lView;return Da(n,t),()=>Bl(n,t)}};function im(){return new Ws(L())}var Ae=class{_console=console;handleError(t){this._console.error("ERROR",t)}},ge=new v("",{providedIn:"root",factory:()=>{let e=p(Y),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(Ae),t.handleError(n))}}}),dd={provide:Ge,useValue:()=>void p(Ae),multi:!0},sm=new v("",{providedIn:"root",factory:()=>{let e=p(q).defaultView;if(!e)return;let t=p(ge),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),p(lt).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function am(){return st([Ml(()=>void p(sm))])}var Tt=class{},nr=new v("",{providedIn:"root",factory:()=>!1});var Aa=new v(""),xa=new v("");var dt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new ee(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new R(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();function rr(...e){}var Oa=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new qs})}return e})(),qs=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function En(e){return{toString:e}.toString()}var qo="__parameters__";function vm(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Gd(e,t,n){return En(()=>{let r=vm(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(qo)?c[qo]:Object.defineProperty(c,qo,{value:[]})[qo];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Wd=oa(Gd("Optional"),8);var qd=oa(Gd("SkipSelf"),4);function Dm(e){return typeof e=="function"}var Ko=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Zd(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var fi=(()=>{let e=()=>Yd;return e.ngInherit=!0,e})();function Yd(e){return e.type.prototype.ngOnChanges&&(e.setInput=Im),Em}function Em(){let e=Kd(this),t=e?.current;if(t){let n=e.previous;if(n===Mt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Im(e,t,n,r,o){let i=this.declaredInputs[r],s=Kd(e)||wm(e,{previous:Mt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Ko(u&&u.currentValue,n,c===Mt),Zd(e,t,o,n)}var Qd="__ngSimpleChanges__";function Kd(e){return e[Qd]||null}function wm(e,t){return e[Qd]=t}var fd=[];var F=function(e,t=null,n){for(let r=0;r<fd.length;r++){let o=fd[r];o(e,t,n)}};function Cm(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Yd(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Jd(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Zo(e,t,n){Xd(e,t,3,n)}function Yo(e,t,n,r){(e[b]&3)===n&&Xd(e,t,n,r)}function Pa(e,t){let n=e[b];(n&3)===t&&(n&=16383,n+=1,e[b]=n)}function Xd(e,t,n,r){let o=r!==void 0?e[xt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[xt]+=65536),(a<i||i==-1)&&(bm(e,n,t,c),e[xt]=(e[xt]&**********)+c+2),c++}function hd(e,t){F(4,e,t);let n=M(null);try{t.call(e)}finally{M(n),F(5,e,t)}}function bm(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[b]>>14<e[xt]>>16&&(e[b]&3)===t&&(e[b]+=16384,hd(a,i)):hd(a,i)}var vn=-1,sr=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Tm(e){return(e.flags&8)!==0}function _m(e){return(e.flags&16)!==0}function Sm(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Nm(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Mm(e){return e===3||e===4||e===6}function Nm(e){return e.charCodeAt(0)===64}function cc(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?pd(e,n,o,null,t[++r]):pd(e,n,o,null,null))}}return e}function pd(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function ef(e){return e!==vn}function Jo(e){return e&32767}function Rm(e){return e>>16}function Xo(e,t){let n=Rm(e),r=t;for(;n>0;)r=r[Rt],n--;return r}var Va=!0;function gd(e){let t=Va;return Va=e,t}var Am=256,tf=Am-1,nf=5,xm=0,ke={};function Om(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(St)&&(r=n[St]),r==null&&(r=n[St]=xm++);let o=r&tf,i=1<<o;t.data[e+(o>>nf)]|=i}function rf(e,t){let n=of(e,t);if(n!==-1)return n;let r=t[S];r.firstCreatePass&&(e.injectorIndex=t.length,ka(r.data,e),ka(t,null),ka(r.blueprint,null));let o=uc(e,t),i=e.injectorIndex;if(ef(o)){let s=Jo(o),a=Xo(o,t),c=a[S].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function ka(e,t){e.push(0,0,0,0,0,0,0,0,t)}function of(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function uc(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=lf(o),r===null)return vn;if(n++,o=o[Rt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return vn}function Pm(e,t,n){Om(e,t,n)}function sf(e,t,n){if(n&8||e!==void 0)return e;Po(t,"NodeInjector")}function af(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[Nt],i=de(void 0);try{return o?o.get(t,r,n&8):ra(t,r,n&8)}finally{de(i)}}return sf(r,t,n)}function cf(e,t,n,r=0,o){if(e!==null){if(t[b]&2048&&!(r&2)){let s=Bm(e,t,n,r,ke);if(s!==ke)return s}let i=uf(e,t,n,r,ke);if(i!==ke)return i}return af(t,n,r,o)}function uf(e,t,n,r,o){let i=Lm(n);if(typeof i=="function"){if(!Ma(t,e,r))return r&1?sf(o,n,r):af(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Po(n);else return s}finally{Na()}}else if(typeof i=="number"){let s=null,a=of(e,t),c=vn,u=r&1?t[he][De]:null;for((a===-1||r&4)&&(c=a===-1?uc(e,t):t[a+8],c===vn||!yd(r,!1)?a=-1:(s=t[S],a=Jo(c),t=Xo(c,t)));a!==-1;){let l=t[S];if(md(i,a,l.data)){let d=km(a,t,n,s,r,u);if(d!==ke)return d}c=t[a+8],c!==vn&&yd(r,t[S].data[a+8]===u)&&md(i,a,t)?(s=l,a=Jo(c),t=Xo(c,t)):a=-1}}return o}function km(e,t,n,r,o,i){let s=t[S],a=s.data[e+8],c=r==null?ct(a)&&Va:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Fm(a,s,n,c,u);return l!==null?Ua(t,s,l,a):ke}function Fm(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&Pt(f)&&f.type===n)return c}return null}function Ua(e,t,n,r){let o=e[n],i=t.data;if(o instanceof sr){let s=o;s.resolving&&na(Tl(i[n]));let a=gd(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],u,l=s.injectImpl?de(s.injectImpl):null,d=Ma(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Cm(n,i[n],t)}finally{l!==null&&de(l),gd(a),s.resolving=!1,Na()}}return o}function Lm(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(St)?e[St]:void 0;return typeof t=="number"?t>=0?t&tf:jm:t}function md(e,t,n){let r=1<<e;return!!(n[t+(e>>nf)]&r)}function yd(e,t){return!(e&2)&&!(e&1&&t)}var Lt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return cf(this._tNode,this._lView,t,wt(r),n)}};function jm(){return new Lt(Ce(),L())}function hi(e){return En(()=>{let t=e.prototype.constructor,n=t[$n]||$a(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[$n]||$a(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function $a(e){return Ys(e)?()=>{let t=$a(ae(e));return t&&t()}:Ct(e)}function Bm(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[b]&2048&&!pn(s);){let a=uf(i,s,n,r|2,ke);if(a!==ke)return a;let c=i.parent;if(!c){let u=s[ha];if(u){let l=u.get(n,ke,r);if(l!==ke)return l}c=lf(s),s=s[Rt]}i=c}return o}function lf(e){let t=e[S],n=t.type;return n===2?t.declTNode:n===1?e[De]:null}function Vm(){return pi(Ce(),L())}function pi(e,t){return new gi(Pe(e,t))}var gi=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Vm}return e})();function df(e){return(e.flags&128)===128}var lc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(lc||{}),ff=new Map,Um=0;function $m(){return Um++}function Hm(e){ff.set(e[Zn],e)}function Ha(e){ff.delete(e[Zn])}var vd="__ngContext__";function dr(e,t){Oe(t)?(e[vd]=t[Zn],Hm(t)):e[vd]=t}function hf(e){return gf(e[hn])}function pf(e){return gf(e[ve])}function gf(e){for(;e!==null&&!Se(e);)e=e[ve];return e}var za;function dc(e){za=e}function mf(){if(za!==void 0)return za;if(typeof document<"u")return document;throw new y(210,!1)}var mi=new v("",{providedIn:"root",factory:()=>zm}),zm="ng",yi=new v(""),In=new v("",{providedIn:"platform",factory:()=>"unknown"});var vi=new v("",{providedIn:"root",factory:()=>mf().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Gm="h",Wm="b";var yf="r";var vf="di";var Df=!1,Ef=new v("",{providedIn:"root",factory:()=>Df});var qm=(e,t,n,r)=>{};function Zm(e,t,n,r){qm(e,t,n,r)}var Ym=()=>null;function If(e,t,n=!1){return Ym(e,t,n)}function wf(e,t){let n=e.contentQueries;if(n!==null){let r=M(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Sa(i),a.contentQueries(2,t[s],s)}}}finally{M(r)}}}function Ga(e,t,n){Sa(0);let r=M(null);try{t(e,n)}finally{M(r)}}function Cf(e,t,n){if(ga(t)){let r=M(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{M(r)}}}var qe=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(qe||{});var ei=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ro})`}};function bf(e){return e instanceof ei?e.changingThisBreaksApplicationSecurity:e}function Tf(e,t){let n=_f(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Ro})`)}return n===t}function _f(e){return e instanceof ei&&e.getTypeName()||null}var Qm=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Sf(e){return e=String(e),e.match(Qm)?e:"unsafe:"+e}var fc=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(fc||{});function Km(e){let t=Jm();return t?t.sanitize(fc.URL,e)||"":Tf(e,"URL")?bf(e):Sf(ln(e))}function Jm(){let e=L();return e&&e[xe].sanitizer}function Mf(e){return e instanceof Function?e():e}function Xm(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Nf="ng-template";function ey(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Xm(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(hc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function hc(e){return e.type===4&&e.value!==Nf}function ty(e,t,n){let r=e.type===4&&!n?Nf:e.value;return t===r}function ny(e,t,n){let r=4,o=e.attrs,i=o!==null?iy(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Me(r)&&!Me(c))return!1;if(s&&Me(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!ty(e,c,n)||c===""&&t.length===1){if(Me(r))return!1;s=!0}}else if(r&8){if(o===null||!ey(e,o,c,n)){if(Me(r))return!1;s=!0}}else{let u=t[++a],l=ry(c,o,hc(e),n);if(l===-1){if(Me(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Me(r))return!1;s=!0}}}}return Me(r)||s}function Me(e){return(e&1)===0}function ry(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return sy(t,e)}function oy(e,t,n=!1){for(let r=0;r<t.length;r++)if(ny(e,t[r],n))return!0;return!1}function iy(e){for(let t=0;t<e.length;t++){let n=e[t];if(Mm(n))return t}return e.length}function sy(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Dd(e,t){return e?":not("+t.trim()+")":t}function ay(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Me(s)&&(t+=Dd(i,o),o=""),r=s,i=i||!Me(r);n++}return o!==""&&(t+=Dd(i,o)),t}function cy(e){return e.map(ay).join(",")}function uy(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Me(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ze={};function ly(e,t){return e.createText(t)}function dy(e,t,n){e.setValue(t,n)}function Rf(e,t,n){return e.createElement(t,n)}function ti(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Af(e,t,n){e.appendChild(t,n)}function Ed(e,t,n,r,o){r!==null?ti(e,t,n,r,o):Af(e,t,n)}function xf(e,t,n){e.removeChild(null,t,n)}function fy(e,t,n){e.setAttribute(t,"style",n)}function hy(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Of(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Sm(e,t,r),o!==null&&hy(e,t,o),i!==null&&fy(e,t,i)}function pc(e,t,n,r,o,i,s,a,c,u,l){let d=Ee+r,h=d+o,f=py(d,h),g=typeof u=="function"?u():u;return f[S]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function py(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ze);return n}function gy(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=pc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function gc(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[_e]=o,d[b]=r|4|128|8|64|1024,(u!==null||e&&e[b]&2048)&&(d[b]|=2048),ya(d),d[Q]=d[Rt]=e,d[W]=n,d[xe]=s||e&&e[xe],d[K]=a||e&&e[K],d[Nt]=c||e&&e[Nt]||null,d[De]=i,d[Zn]=$m(),d[dn]=l,d[ha]=u,d[he]=t.type==2?e[he]:d,d}function my(e,t,n){let r=Pe(t,e),o=gy(n),i=e[xe].rendererFactory,s=mc(e,gc(e,o,null,Pf(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Pf(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function kf(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function mc(e,t){return e[hn]?e[fa][ve]=t:e[hn]=t,e[fa]=t,t}function yy(e=1){Ff(We(),L(),mn()+e,!1)}function Ff(e,t,n,r){if(!r)if((t[b]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Zo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Yo(t,i,0,n)}ut(n)}var Di=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Di||{});function Wa(e,t,n,r){let o=M(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Di.SignalBased)!==0&&(c=t[i][Zr]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Zd(t,c,i,r)}finally{M(o)}}function Lf(e,t,n,r,o){let i=mn(),s=r&2;try{ut(-1),s&&t.length>Ee&&Ff(e,t,Ee,!1),F(s?2:0,o,n),n(r,o)}finally{ut(i),F(s?3:1,o,n)}}function yc(e,t,n){Ty(e,t,n),(n.flags&64)===64&&_y(e,t,n)}function jf(e,t,n=Pe){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function vy(e,t,n,r){let i=r.get(Ef,Df)||n===qe.ShadowDom,s=e.selectRootElement(t,i);return Dy(s),s}function Dy(e){Ey(e)}var Ey=()=>null;function Iy(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function wy(e,t,n,r,o,i){let s=t[S];if(vc(e,s,t,n,r)){ct(e)&&by(t,e.index);return}Cy(e,t,n,r,o,i)}function Cy(e,t,n,r,o,i){if(e.type&3){let s=Pe(e,t);n=Iy(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function by(e,t){let n=we(t,e);n[b]&16||(n[b]|=64)}function Ty(e,t,n){let r=n.directiveStart,o=n.directiveEnd;ct(n)&&my(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||rf(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Ua(t,e,s,n);if(dr(c,t),i!==null&&My(t,s-r,c,a,n,i),Pt(a)){let u=we(n.index,t);u[W]=Ua(t,e,s,n)}}}function _y(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=td();try{ut(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];$o(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Sy(c,u)}}finally{ut(-1),$o(s)}}function Sy(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Bf(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];oy(t,i.selectors,!1)&&(r??=[],Pt(i)?r.unshift(i):r.push(i))}return r}function My(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Wa(r,n,c,u)}}function Ny(e,t){let n=e[Nt];if(!n)return;n.get(ge,null)?.(t)}function vc(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];Wa(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Wa(l,u,r,o),a=!0}return a}function Ry(e,t){let n=we(t,e),r=n[S];Ay(r,n);let o=n[_e];o!==null&&n[dn]===null&&(n[dn]=If(o,n[Nt])),F(18),Dc(r,n,n[W]),F(19,n[W])}function Ay(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Dc(e,t,n){Ho(t);try{let r=e.viewQuery;r!==null&&Ga(1,r,n);let o=e.template;o!==null&&Lf(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ot]?.finishViewCreation(e),e.staticContentQueries&&wf(e,t),e.staticViewQueries&&Ga(2,e.viewQuery,n);let i=e.components;i!==null&&xy(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[b]&=-5,zo()}}function xy(e,t){for(let n=0;n<t.length;n++)Ry(e,t[n])}function Ec(e,t,n,r){let o=M(null);try{let i=t.tView,a=e[b]&4096?4096:16,c=gc(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[At]=u;let l=e[Ot];return l!==null&&(c[Ot]=l.createEmbeddedView(i)),Dc(i,c,n),c}finally{M(o)}}function ni(e,t){return!t||t.firstChild===null||df(e)}var Id=!1,Oy=new v(""),Py;function Ic(e,t){return Py(e,t)}var ht=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ht||{});function wc(e){return(e.flags&32)===32}function yn(e,t,n,r,o){if(r!=null){let i,s=!1;Se(r)?i=r:Oe(r)&&(s=!0,r=r[_e]);let a=Ie(r);e===0&&n!==null?o==null?Af(t,n,a):ti(t,n,a,o||null,!0):e===1&&n!==null?ti(t,n,a,o||null,!0):e===2?xf(t,a,s):e===3&&t.destroyNode(a),i!=null&&Wy(t,e,i,n,o)}}function ky(e,t){Vf(e,t),t[_e]=null,t[De]=null}function Fy(e,t,n,r,o,i){r[_e]=o,r[De]=t,Ii(e,r,n,1,o,i)}function Vf(e,t){t[xe].changeDetectionScheduler?.notify(9),Ii(e,t,t[K],2,null,null)}function Ly(e){let t=e[hn];if(!t)return Fa(e[S],e);for(;t;){let n=null;if(Oe(t))n=t[hn];else{let r=t[te];r&&(n=r)}if(!n){for(;t&&!t[ve]&&t!==e;)Oe(t)&&Fa(t[S],t),t=t[Q];t===null&&(t=e),Oe(t)&&Fa(t[S],t),n=t&&t[ve]}t=n}}function Cc(e,t){let n=e[Kn],r=n.indexOf(t);n.splice(r,1)}function Ei(e,t){if(kt(t))return;let n=t[K];n.destroyNode&&Ii(e,t,n,3,null,null),Ly(t)}function Fa(e,t){if(kt(t))return;let n=M(null);try{t[b]&=-129,t[b]|=256,t[pe]&&Ds(t[pe]),By(e,t),jy(e,t),t[S].type===1&&t[K].destroy();let r=t[At];if(r!==null&&Se(t[Q])){r!==t[Q]&&Cc(r,t);let o=t[Ot];o!==null&&o.detachView(e)}Ha(t)}finally{M(n)}}function jy(e,t){let n=e.cleanup,r=t[fn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[fn]=null);let o=t[ze];if(o!==null){t[ze]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Yn];if(i!==null){t[Yn]=null;for(let s of i)s.destroy()}}function By(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof sr)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];F(4,a,c);try{c.call(a)}finally{F(5,a,c)}}else{F(4,o,i);try{i.call(o)}finally{F(5,o,i)}}}}}function Vy(e,t,n){return Uy(e,t.parent,n)}function Uy(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[_e];if(ct(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===qe.None||o===qe.Emulated)return null}return Pe(r,n)}function $y(e,t,n){return zy(e,t,n)}function Hy(e,t,n){return e.type&40?Pe(e,n):null}var zy=Hy,wd;function bc(e,t,n,r){let o=Vy(e,r,t),i=t[K],s=r.parent||t[De],a=$y(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Ed(i,o,n[c],a,!1);else Ed(i,o,n,a,!1);wd!==void 0&&wd(i,r,t,n,o)}function or(e,t){if(t!==null){let n=t.type;if(n&3)return Pe(t,e);if(n&4)return qa(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return or(e,r);{let o=e[t.index];return Se(o)?qa(-1,o):Ie(o)}}else{if(n&128)return or(e,t.next);if(n&32)return Ic(t,e)()||Ie(e[t.index]);{let r=Uf(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=ot(e[he]);return or(o,r)}else return or(e,t.next)}}}return null}function Uf(e,t){if(t!==null){let r=e[he][De],o=t.projection;return r.projection[o]}return null}function qa(e,t){let n=te+e+1;if(n<t.length){let r=t[n],o=r[S].firstChild;if(o!==null)return or(r,o)}return t[at]}function Tc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&dr(Ie(a),r),n.flags|=2),!wc(n))if(c&8)Tc(e,t,n.child,r,o,i,!1),yn(t,e,o,a,i);else if(c&32){let u=Ic(n,r),l;for(;l=u();)yn(t,e,o,l,i);yn(t,e,o,a,i)}else c&16?Gy(e,t,r,n,o,i):yn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Ii(e,t,n,r,o,i){Tc(n,r,e.firstChild,t,o,i,!1)}function Gy(e,t,n,r,o,i){let s=n[he],c=s[De].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];yn(t,e,o,l,i)}else{let u=c,l=s[Q];df(r)&&(u.flags|=128),Tc(e,t,u,l,o,i,!0)}}function Wy(e,t,n,r,o){let i=n[at],s=Ie(n);i!==s&&yn(t,e,r,i,o);for(let a=te;a<n.length;a++){let c=n[a];Ii(c[S],c,e,t,r,i)}}function ar(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Ie(i)),Se(i)&&$f(i,r);let s=n.type;if(s&8)ar(e,t,n.child,r);else if(s&32){let a=Ic(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Uf(t,n);if(Array.isArray(a))r.push(...a);else{let c=ot(t[he]);ar(c[S],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function $f(e,t){for(let n=te;n<e.length;n++){let r=e[n],o=r[S].firstChild;o!==null&&ar(r[S],r,o,t)}e[at]!==e[_e]&&t.push(e[at])}function Hf(e){if(e[Lo]!==null){for(let t of e[Lo])t.impl.addSequence(t);e[Lo].length=0}}var zf=[];function qy(e){return e[pe]??Zy(e)}function Zy(e){let t=zf.pop()??Object.create(Qy);return t.lView=e,t}function Yy(e){e.lView[pe]!==e&&(e.lView=null,zf.push(e))}var Qy=j(m({},Yr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{er(e.lView)},consumerOnSignalRead(){this.lView[pe]=this}});function Ky(e){let t=e[pe]??Object.create(Jy);return t.lView=e,t}var Jy=j(m({},Yr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=ot(e.lView);for(;t&&!Gf(t[S]);)t=ot(t);t&&va(t)},consumerOnSignalRead(){this.lView[pe]=this}});function Gf(e){return e.type!==2}function Wf(e){if(e[Yn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Yn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[b]&8192)}}var Xy=100;function _c(e,t=0){let r=e[xe].rendererFactory,o=!1;o||r.begin?.();try{ev(e,t)}finally{o||r.end?.()}}function ev(e,t){let n=ba();try{Ta(!0),Za(e,t);let r=0;for(;Xn(e);){if(r===Xy)throw new y(103,!1);r++,Za(e,1)}}finally{Ta(n)}}function qf(e,t){Ca(t?tr.Exhaustive:tr.OnlyDirtyViews);try{_c(e)}finally{Ca(tr.Off)}}function tv(e,t,n,r){if(kt(t))return;let o=t[b],i=!1,s=!1;Ho(t);let a=!0,c=null,u=null;i||(Gf(e)?(u=qy(t),c=Qr(u)):gs()===null?(a=!1,u=Ky(t),c=Qr(u)):t[pe]&&(Ds(t[pe]),t[pe]=null));try{ya(t),Kl(e.bindingStartIndex),n!==null&&Lf(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Zo(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Yo(t,f,0,null),Pa(t,0)}if(s||nv(t),Wf(t),Zf(t,0),e.contentQueries!==null&&wf(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Zo(t,f)}else{let f=e.contentHooks;f!==null&&Yo(t,f,1),Pa(t,1)}ov(e,t);let d=e.components;d!==null&&Qf(t,d,0);let h=e.viewQuery;if(h!==null&&Ga(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Zo(t,f)}else{let f=e.viewHooks;f!==null&&Yo(t,f,2),Pa(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Fo]){for(let f of t[Fo])f();t[Fo]=null}i||(Hf(t),t[b]&=-73)}catch(l){throw i||er(t),l}finally{u!==null&&(vs(u,c),a&&Yy(u)),zo()}}function Zf(e,t){for(let n=hf(e);n!==null;n=pf(n))for(let r=te;r<n.length;r++){let o=n[r];Yf(o,t)}}function nv(e){for(let t=hf(e);t!==null;t=pf(t)){if(!(t[b]&2))continue;let n=t[Kn];for(let r=0;r<n.length;r++){let o=n[r];va(o)}}}function rv(e,t,n){F(18);let r=we(t,e);Yf(r,n),F(19,r[W])}function Yf(e,t){Bo(e)&&Za(e,t)}function Za(e,t){let r=e[S],o=e[b],i=e[pe],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Kr(i)),s||=!1,i&&(i.dirty=!1),e[b]&=-9217,s)tv(r,e,r.template,e[W]);else if(o&8192){let a=M(null);try{Wf(e),Zf(e,1);let c=r.components;c!==null&&Qf(e,c,1),Hf(e)}finally{M(a)}}}function Qf(e,t,n){for(let r=0;r<t.length;r++)rv(e,t[r],n)}function ov(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)ut(~o);else{let i=o,s=n[++r],a=n[++r];ed(s,i);let c=t[i];F(24,c),a(2,c),F(25,c)}}}finally{ut(-1)}}function Sc(e,t){let n=ba()?64:1088;for(e[xe].changeDetectionScheduler?.notify(t);e;){e[b]|=n;let r=ot(e);if(pn(e)&&!r)return e;e=r}return null}function Kf(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function iv(e,t){let n=te+t;if(n<e.length)return e[n]}function Mc(e,t,n,r=!0){let o=t[S];if(av(o,t,e,n),r){let s=qa(n,e),a=t[K],c=a.parentNode(e[at]);c!==null&&Fy(o,e[De],a,t,c,s)}let i=t[dn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function sv(e,t){let n=cr(e,t);return n!==void 0&&Ei(n[S],n),n}function cr(e,t){if(e.length<=te)return;let n=te+t,r=e[n];if(r){let o=r[At];o!==null&&o!==e&&Cc(o,r),t>0&&(e[n-1][ve]=r[ve]);let i=Gn(e,te+t);ky(r[S],r);let s=i[Ot];s!==null&&s.detachView(i[S]),r[Q]=null,r[ve]=null,r[b]&=-129}return r}function av(e,t,n,r){let o=te+r,i=n.length;r>0&&(n[o-1][ve]=t),r<i-te?(t[ve]=n[o],ia(n,te+r,t)):(n.push(t),t[ve]=null),t[Q]=n;let s=t[At];s!==null&&n!==s&&Jf(s,t);let a=t[Ot];a!==null&&a.insertView(e),Vo(t),t[b]|=128}function Jf(e,t){let n=e[Kn],r=t[Q];if(Oe(r))e[b]|=2;else{let o=r[Q][he];t[he]!==o&&(e[b]|=2)}n===null?e[Kn]=[t]:n.push(t)}var ft=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[S];return ar(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[W]}set context(t){this._lView[W]=t}get destroyed(){return kt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Q];if(Se(t)){let n=t[Qn],r=n?n.indexOf(this):-1;r>-1&&(cr(t,r),Gn(n,r))}this._attachedToViewContainer=!1}Ei(this._lView[S],this._lView)}onDestroy(t){Da(this._lView,t)}markForCheck(){Sc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[b]&=-129}reattach(){Vo(this._lView),this._lView[b]|=128}detectChanges(){this._lView[b]|=1024,_c(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Nt].get(Oy,Id)}catch{this.exhaustive=Id}}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=pn(this._lView),n=this._lView[At];n!==null&&!t&&Cc(n,this._lView),Vf(this._lView[S],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=t;let n=pn(this._lView),r=this._lView[At];r!==null&&!n&&Jf(r,this._lView),Vo(this._lView)}};var fr=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=cv;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Ec(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new ft(i)}}return e})();function cv(){return uv(Ce(),L())}function uv(e,t){return e.type&4?new fr(t,e,pi(e,t)):null}function Nc(e,t,n,r,o){let i=e.data[t];if(i===null)i=lv(e,t,n,r,o),Xl()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Zl();i.injectorIndex=s===null?-1:s.injectorIndex}return gn(i,!0),i}function lv(e,t,n,r,o){let i=Ia(),s=wa(),a=s?i:i&&i.parent,c=e.data[t]=fv(e,a,n,t,r,o);return dv(e,c,i,s),c}function dv(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function fv(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Gl()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var BN=new RegExp(`^(\\d+)*(${Wm}|${Gm})*(.*)`);function hv(e){let t=e[pa]??[],r=e[Q][K],o=[];for(let i of t)i.data[vf]!==void 0?o.push(i):pv(i,r);e[pa]=o}function pv(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[yf];for(;n<o;){let i=r.nextSibling;xf(t,r,!1),r=i,n++}}}var gv=()=>null,mv=()=>null;function Ya(e,t){return gv(e,t)}function yv(e,t,n){return mv(e,t,n)}var Xf=class{},wi=class{},Qa=class{resolveComponentFactory(t){throw new y(917,!1)}},hr=class{static NULL=new Qa},jt=class{},Rc=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>vv()}return e})();function vv(){let e=L(),t=Ce(),n=we(t.index,e);return(Oe(n)?n:e)[K]}var eh=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var Qo={},Ka=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,Qo,r);return o!==Qo||n===Qo?o:this.parentInjector.get(t,n,r)}};function Cd(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Zs(o,a);else if(i==2){let c=a,u=t[++s];r=Zs(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Fe(e,t=0){let n=L();if(n===null)return C(e,t);let r=Ce();return cf(r,n,ae(e),t)}function th(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}Iv(e,t,n,a,i,c,u)}i!==null&&r!==null&&Dv(n,r,i)}function Dv(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new y(-301,!1);r.push(t[o],i)}}function Ev(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Iv(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&Pt(f)&&(c=!0,Ev(e,n,h)),Pm(rf(n,t),e,f.type)}Sv(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=kf(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=cc(n.mergedAttrs,f.hostAttrs),Cv(e,n,t,d,f),_v(d,f,o),s!==null&&s.has(f)){let[I,P]=s.get(f);n.directiveToIndex.set(f.type,[d,I+n.directiveStart,P+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}wv(e,n,i)}function wv(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))bd(0,t,o,r),bd(1,t,o,r),_d(t,r,!1);else{let i=n.get(o);Td(0,t,i,r),Td(1,t,i,r),_d(t,r,!0)}}}function bd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),nh(t,i)}}function Td(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),nh(t,s)}}function nh(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function _d(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||hc(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Cv(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ct(o.type,!0)),s=new sr(i,Pt(o),Fe);e.blueprint[r]=s,n[r]=s,bv(e,t,r,kf(e,n,o.hostVars,Ze),o)}function bv(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Tv(s)!=a&&s.push(a),s.push(n,r,i)}}function Tv(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function _v(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Pt(t)&&(n[""]=e)}}function Sv(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function rh(e,t,n,r,o,i,s,a){let c=t.consts,u=Ft(c,s),l=Nc(t,e,2,r,u);return i&&th(t,n,l,Ft(c,a),o),l.mergedAttrs=cc(l.mergedAttrs,l.attrs),l.attrs!==null&&Cd(l,l.attrs,!1),l.mergedAttrs!==null&&Cd(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function oh(e,t){Jd(e,t),ga(t)&&e.queries.elementEnd(t)}function Ac(e){return sh(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function ih(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function sh(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function ah(e,t,n){return e[t]=n}function ch(e,t){return e[t]}function ur(e,t,n){if(n===Ze)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function ri(e,t,n,r){let o=ur(e,t,n);return ur(e,t+1,r)||o}function Mv(e,t,n,r,o,i){let s=ri(e,t,n,r);return ri(e,t+2,o,i)||s}function La(e,t,n){return function r(o){let i=ct(e)?we(e.index,t):t;Sc(i,5);let s=t[W],a=Sd(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Sd(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Sd(e,t,n,r){let o=M(null);try{return F(6,t,n),n(r)!==!1}catch(i){return Ny(e,i),!1}finally{F(7,t,n),M(o)}}function Nv(e,t,n,r,o,i,s,a){let c=jo(e),u=!1,l=null;if(!r&&c&&(l=Rv(t,n,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Pe(e,n),h=r?r(d):d;Zm(n,h,i,a);let f=o.listen(h,i,a),g=r?I=>r(Ie(I[e.index])):e.index;uh(g,t,n,i,a,f,!1)}return u}function Rv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[fn],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function uh(e,t,n,r,o,i,s){let a=t.firstCreatePass?Ul(t):null,c=Vl(n),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Md(e,t,n,r,o,i){let s=t[n],a=t[S],u=a.data[n].outputs[r],d=s[u].subscribe(i);uh(e.index,a,t,o,i,d,!0)}var Ja=Symbol("BINDING");var oi=class extends hr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=it(t);return new Dn(n,this.ngModule)}};function Av(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Di.SignalBased)!==0};return o&&(i.transform=o),i})}function xv(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Ov(e,t,n){let r=t instanceof Y?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ka(n,r):n}function Pv(e){let t=e.get(jt,null);if(t===null)throw new y(407,!1);let n=e.get(eh,null),r=e.get(Tt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function kv(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Rf(t,n,n==="svg"?ma:n==="math"?kl:null)}var Dn=class extends wi{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Av(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=xv(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=cy(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){F(22);let a=M(null);try{let c=this.componentDef,u=Fv(r,c,s,i),l=Ov(c,o||this.ngModule,t),d=Pv(l),h=d.rendererFactory.createRenderer(null,c),f=r?vy(h,r,c.encapsulation,l):kv(c,h),g=s?.some(Nd)||i?.some(x=>typeof x!="function"&&x.bindings.some(Nd)),I=gc(null,u,null,512|Pf(c),null,null,d,h,l,null,If(f,l,!0));I[Ee]=f,Ho(I);let P=null;try{let x=rh(Ee,u,I,"#host",()=>u.directiveRegistry,!0,0);f&&(Of(h,f,x),dr(f,I)),yc(u,I,x),Cf(u,x,I),oh(u,x),n!==void 0&&jv(x,this.ngContentSelectors,n),P=we(x.index,I),I[W]=P[W],Dc(u,I,null)}catch(x){throw P!==null&&Ha(P),Ha(I),x}finally{F(23),zo()}return new ii(this.componentType,I,!!g)}finally{M(a)}}};function Fv(e,t,n,r){let o=e?["ng-version","20.0.6"]:uy(t.selectors[0]),i=null,s=null,a=0;if(n)for(let l of n)a+=l[Ja].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[Ja].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[t];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=ua(d);c.push(h)}return pc(0,null,Lv(i,s),1,a,c,null,null,null,[o],null)}function Lv(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Nd(e){let t=e[Ja].kind;return t==="input"||t==="twoWay"}var ii=class extends Xf{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=Jn(n[S],Ee),this.location=pi(this._tNode,n),this.instance=we(this._tNode.index,n)[W],this.hostView=this.changeDetectorRef=new ft(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=vc(r,o[S],o,t,n);this.previousInputValues.set(t,n);let s=we(r.index,o);Sc(s,1)}get injector(){return new Lt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function jv(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Vt=(()=>{class e{static __NG_ELEMENT_ID__=Bv}return e})();function Bv(){let e=Ce();return Uv(e,L())}var Vv=Vt,lh=class extends Vv{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return pi(this._hostTNode,this._hostLView)}get injector(){return new Lt(this._hostTNode,this._hostLView)}get parentInjector(){let t=uc(this._hostTNode,this._hostLView);if(ef(t)){let n=Xo(t,this._hostLView),r=Jo(t),o=n[S].data[r+8];return new Lt(o,n)}else return new Lt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Rd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-te}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Ya(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,ni(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!Dm(t),u;if(c)u=n;else{let P=n||{};u=P.index,r=P.injector,o=P.projectableNodes,i=P.environmentInjector||P.ngModuleRef,s=P.directives,a=P.bindings}let l=c?t:new Dn(it(t)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let x=(c?d:this.parentInjector).get(Y,null);x&&(i=x)}let h=it(l.componentType??{}),f=Ya(this._lContainer,h?.id??null),g=f?.firstChild??null,I=l.create(d,o,g,i,s,a);return this.insertImpl(I.hostView,u,ni(this._hostTNode,f)),I}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Ll(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[Q],u=new lh(c,c[De],c[Q]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Mc(s,o,i,r),t.attachToViewContainerRef(),ia(ja(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Rd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=cr(this._lContainer,n);r&&(Gn(ja(this._lContainer),n),Ei(r[S],r))}detach(t){let n=this._adjustIndex(t,-1),r=cr(this._lContainer,n);return r&&Gn(ja(this._lContainer),n)!=null?new ft(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Rd(e){return e[Qn]}function ja(e){return e[Qn]||(e[Qn]=[])}function Uv(e,t){let n,r=t[e.index];return Se(r)?n=r:(n=Kf(r,t,null,e),t[e.index]=n,mc(t,n)),Hv(n,t,e,r),new lh(n,e,t)}function $v(e,t){let n=e[K],r=n.createComment(""),o=Pe(t,e),i=n.parentNode(o);return ti(n,i,r,n.nextSibling(o),!1),r}var Hv=Wv,zv=()=>!1;function Gv(e,t,n){return zv(e,t,n)}function Wv(e,t,n,r){if(e[at])return;let o;n.type&8?o=Ie(r):o=$v(t,n),e[at]=o}var Ad=new Set;function pr(e){Ad.has(e)||(Ad.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Bt=class{},Ci=class{};var si=class extends Bt{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new oi(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=ca(t);this._bootstrapComponents=Mf(i.bootstrap),this._r3Injector=Ra(t,n,[{provide:Bt,useValue:this},{provide:hr,useValue:this.componentFactoryResolver},...r],fe(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ai=class extends Ci{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new si(this.moduleType,t,[])}};var lr=class extends Bt{injector;componentFactoryResolver=new oi(this);instance=null;constructor(t){super();let n=new bt([...t.providers,{provide:Bt,useValue:this},{provide:hr,useValue:this.componentFactoryResolver}],t.parent||qn(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function gr(e,t,n=null){return new lr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var qv=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=la(!1,n.type),o=r.length>0?gr([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(C(Y))})}return e})();function xc(e){return En(()=>{let t=dh(e),n=j(m({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===lc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(qv).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||qe.Emulated,styles:e.styles||Te,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&pr("NgStandalone"),fh(n);let r=e.dependencies;return n.directiveDefs=xd(r,!1),n.pipeDefs=xd(r,!0),n.id=Jv(n),n})}function Zv(e){return it(e)||ua(e)}function Yv(e){return e!==null}function wn(e){return En(()=>({type:e.type,bootstrap:e.bootstrap||Te,declarations:e.declarations||Te,imports:e.imports||Te,exports:e.exports||Te,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Qv(e,t){if(e==null)return Mt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Di.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Kv(e){if(e==null)return Mt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function Ut(e){return En(()=>{let t=dh(e);return fh(t),t})}function dh(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Mt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Te,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Qv(e.inputs,t),outputs:Kv(e.outputs),debugInfo:null}}function fh(e){e.features?.forEach(t=>t(e))}function xd(e,t){if(!e)return null;let n=t?Sl:Zv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Yv)}function Jv(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Xv(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Nc(t,e,4,s||null,a||null);Ea()&&th(t,n,l,Ft(u,c),Bf),l.mergedAttrs=cc(l.mergedAttrs,l.attrs),Jd(t,l);let d=l.tView=pc(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Xa(e,t,n,r,o,i,s,a,c,u,l){let d=n+Ee,h=t.firstCreatePass?Xv(d,t,e,r,o,i,s,a,u):t.data[d];c&&(h.flags|=c),gn(h,!1);let f=eD(t,e,h,n);Go()&&bc(t,e,f,h),dr(f,e);let g=Kf(f,e,f,h);return e[d]=g,mc(e,g),Gv(g,h,e),jo(h)&&yc(t,e,h),u!=null&&jf(e,h,l),h}function hh(e,t,n,r,o,i,s,a){let c=L(),u=We(),l=Ft(u.consts,i);return Xa(c,u,e,t,n,r,o,l,void 0,s,a),hh}var eD=tD;function tD(e,t,n,r){return Wo(!0),t[K].createComment("")}var Oc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Oc||{}),mr=new v(""),ph=!1,ec=class extends X{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Ol()&&(this.destroyRef=p(lt,{optional:!0})??void 0,this.pendingTasks=p(dt,{optional:!0})??void 0)}emit(t){let n=M(null);try{super.next(t)}finally{M(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof V&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ne=ec;function gh(e){let t,n;function r(){e=rr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Od(e){return queueMicrotask(()=>e()),()=>{e=rr}}var Pc="isAngularZone",ci=Pc+"_ID",nD=0,H=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ne(!1);onMicrotaskEmpty=new ne(!1);onStable=new ne(!1);onError=new ne(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=ph}=t;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,iD(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Pc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,rD,rr,rr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},rD={};function kc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function oD(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){gh(()=>{e.callbackScheduled=!1,tc(e),e.isCheckStableRunning=!0,kc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),tc(e)}function iD(e){let t=()=>{oD(e)},n=nD++;e._inner=e._inner.fork({name:"angular",properties:{[Pc]:!0,[ci]:n,[ci+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(sD(c))return r.invokeTask(i,s,a,c);try{return Pd(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),kd(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Pd(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!aD(c)&&t(),kd(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,tc(e),kc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function tc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Pd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function kd(e){e._nesting--,kc(e)}var ui=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ne;onMicrotaskEmpty=new ne;onStable=new ne;onError=new ne;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function sD(e){return mh(e,"__ignore_ng_zone__")}function aD(e){return mh(e,"__scheduler_tick__")}function mh(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var yh=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();var Fc=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Lc=new v("");function Cn(e){return!!e&&typeof e.then=="function"}function jc(e){return!!e&&typeof e.subscribe=="function"}var vh=new v("");var Bc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(vh,{optional:!0})??[];injector=p(Re);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=ce(this.injector,o);if(Cn(i))n.push(i);else if(jc(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bi=new v("");function Dh(){Es(()=>{let e="";throw new y(600,e)})}function Eh(e){return e.isBoundToModule}var cD=10;var $t=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(ge);afterRenderManager=p(yh);zonelessEnabled=p(nr);rootEffectScheduler=p(Oa);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new X;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(dt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(_(n=>!n))}constructor(){p(mr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(Y);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Re.NULL){return this._injector.get(H).run(()=>{F(10);let s=n instanceof wi;if(!this._injector.get(Bc).done){let g="";throw new y(405,g)}let c;s?c=n:c=this._injector.get(hr).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let u=Eh(c)?void 0:this._injector.get(Bt),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(Lc,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),ir(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),F(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){F(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Oc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let n=M(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,M(n),this.afterTick.next(),F(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(jt,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<cD;)F(14),this.synchronizeOnce(),F(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Xn(o))continue;let i=r&&!this.zonelessEnabled?0:1;_c(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Xn(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ir(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(bi,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ir(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new y(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ir(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}var nc=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Ba(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function uD(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=Ba(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),f=t[c],g=Ba(s,h,c,f,n);if(g!==0){g<0&&e.updateValue(s,f),s--,c--;continue}let I=n(i,u),P=n(s,h),x=n(i,l);if(Object.is(x,P)){let yt=n(c,f);Object.is(yt,I)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new li,o??=Ld(e,i,s,n),rc(e,r,i,x))e.updateValue(i,l),i++,s++;else if(o.has(x))r.set(I,e.detach(i)),s--;else{let yt=e.create(i,t[i]);e.attach(i,yt),i++,s++}}for(;i<=c;)Fd(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,h=Ba(i,l,i,d,n);if(h!==0)h<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new li,o??=Ld(e,i,s,n);let f=n(i,d);if(rc(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let g=n(i,l);r.set(g,e.detach(i)),s--}}}for(;!u.done;)Fd(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function rc(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Fd(e,t,n,r,o){if(rc(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Ld(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var li=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};var oc=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-te}};var ic=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function lD(e,t,n,r,o,i,s,a,c,u,l,d,h){pr("NgControlFlow");let f=L(),g=We(),I=c!==void 0,P=L(),x=a?s.bind(P[he][W]):s,yt=new ic(I,x);P[Ee+e]=yt,Xa(f,g,e+1,t,n,r,o,Ft(g.consts,i),256),I&&Xa(f,g,e+2,c,u,l,d,Ft(g.consts,h),512)}var sc=class extends nc{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-te}at(t){return this.getLView(t)[W].$implicit}attach(t,n){let r=n[dn];this.needsIndexUpdate||=t!==this.length,Mc(this.lContainer,n,t,ni(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,fD(this.lContainer,t)}create(t,n){let r=Ya(this.lContainer,this.templateTNode.tView.ssrId),o=Ec(this.hostLView,this.templateTNode,new oc(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){Ei(t[S],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[W].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[W].$index=t}getLView(t){return hD(this.lContainer,t)}};function dD(e){let t=M(null),n=mn();try{let r=L(),o=r[S],i=r[n],s=n+1,a=jd(r,s);if(i.liveCollection===void 0){let u=Bd(o,s);i.liveCollection=new sc(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(uD(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Uo(),l=c.length===0;if(ur(r,u,l)){let d=n+2,h=jd(r,d);if(l){let f=Bd(o,d),g=yv(h,f,r),I=Ec(r,f,void 0,{dehydratedView:g});Mc(h,I,0,ni(f,g))}else o.firstUpdatePass&&hv(h),sv(h,0)}}}finally{M(t)}}function jd(e,t){return e[t]}function fD(e,t){return cr(e,t)}function hD(e,t){return iv(e,t)}function Bd(e,t){return Jn(e,t)}function Ih(e,t,n){let r=L(),o=Uo();if(ur(r,o,t)){let i=We(),s=sd();wy(s,r,e,t,r[K],n)}return Ih}function Vd(e,t,n,r,o){vc(t,e,n,o?"class":"style",r)}function Vc(e,t,n,r){let o=L(),i=We(),s=Ee+e,a=o[K],c=i.firstCreatePass?rh(s,i,o,t,Bf,Ea(),n,r):i.data[s],u=pD(i,o,c,a,t,e);o[s]=u;let l=jo(c);return gn(c,!0),Of(a,u,c),!wc(c)&&Go()&&bc(i,o,u,c),($l()===0||l)&&dr(u,o),Hl(),l&&(yc(i,o,c),Cf(i,c,o)),r!==null&&jf(o,c),Vc}function Uc(){let e=Ce();wa()?Yl():(e=e.parent,gn(e,!1));let t=e;Wl(t)&&ql(),zl();let n=We();return n.firstCreatePass&&oh(n,t),t.classesWithoutHost!=null&&Tm(t)&&Vd(n,t,L(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&_m(t)&&Vd(n,t,L(),t.stylesWithoutHost,!1),Uc}function Ti(e,t,n,r){return Vc(e,t,n,r),Uc(),Ti}var pD=(e,t,n,r,o,i)=>(Wo(!0),Rf(r,o,ud()));var yr="en-US";var gD=yr;function wh(e){typeof e=="string"&&(gD=e.toLowerCase().replace(/_/g,"-"))}function Ch(e,t,n){let r=L(),o=We(),i=Ce();return mD(o,r,r[K],i,e,t,n),Ch}function mD(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=La(r,t,i),Nv(r,e,t,s,n,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=La(r,t,i),Md(r,t,h,f,o,c)}if(u&&u.length)for(let d of u)c??=La(r,t,i),Md(r,t,d,o,o,c)}}function yD(e=1){return id(e)}function vD(e,t=""){let n=L(),r=We(),o=e+Ee,i=r.firstCreatePass?Nc(r,o,1,t,null):r.data[o],s=DD(r,n,i,t,e);n[o]=s,Go()&&bc(r,n,s,i),gn(i,!1)}var DD=(e,t,n,r,o)=>(Wo(!0),ly(t[K],r));function ED(e,t,n,r=""){return ur(e,Uo(),n)?t+ln(n)+r:Ze}function ID(e,t,n,r,o,i=""){let s=Ql(),a=ri(e,s,n,o);return Jl(2),a?t+ln(n)+r+ln(o)+i:Ze}function bh(e){return $c("",e),bh}function $c(e,t,n){let r=L(),o=ED(r,e,t,n);return o!==Ze&&_h(r,mn(),o),$c}function Th(e,t,n,r,o){let i=L(),s=ID(i,e,t,n,r,o);return s!==Ze&&_h(i,mn(),s),Th}function _h(e,t,n){let r=Fl(t,e);dy(e[K],r,n)}function wD(e,t,n){let r=_a()+e,o=L();return o[r]===Ze?ah(o,r,n?t.call(n):t()):ch(o,r)}function CD(e,t,n,r,o,i,s,a,c){let u=_a()+e,l=L(),d=Mv(l,u,n,r,o,i);return ri(l,u+4,s,a)||d?ah(l,u+6,c?t.call(c,n,r,o,i,s,a):t(n,r,o,i,s,a)):ch(l,u+6)}var di=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Hc=(()=>{class e{compileModuleSync(n){return new ai(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=ca(n),i=Mf(o.declarations).reduce((s,a)=>{let c=it(a);return c&&s.push(new Dn(c)),s},[]);return new di(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var bD=(()=>{class e{zone=p(H);changeDetectionScheduler=p(Tt);applicationRef=p($t);applicationErrorHandler=p(ge);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Sh=new v("",{factory:()=>!1});function zc({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new H(j(m({},Gc()),{scheduleInRootZone:n})),[{provide:H,useFactory:e},{provide:Ge,multi:!0,useFactory:()=>{let r=p(bD,{optional:!0});return()=>r.initialize()}},{provide:Ge,multi:!0,useFactory:()=>{let r=p(_D);return()=>{r.initialize()}}},t===!0?{provide:Aa,useValue:!0}:[],{provide:xa,useValue:n??ph},{provide:ge,useFactory:()=>{let r=p(H),o=p(Y),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Ae),i.handleError(s))})}}}]}function TD(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=zc({ngZoneFactory:()=>{let o=Gc(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&pr("NgZone_CoalesceEvent"),new H(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return st([{provide:Sh,useValue:!0},{provide:nr,useValue:!1},r])}function Gc(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var _D=(()=>{class e{subscription=new V;initialized=!1;zone=p(H);pendingTasks=p(dt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{H.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Mh=(()=>{class e{applicationErrorHandler=p(ge);appRef=p($t);taskService=p(dt);ngZone=p(H);zonelessEnabled=p(nr);tracing=p(mr,{optional:!0});disableScheduling=p(Aa,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new V;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ci):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(xa,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ui||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Od:gh;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ci+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Od(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function SD(){return typeof $localize<"u"&&$localize.locale||yr}var _i=new v("",{providedIn:"root",factory:()=>p(_i,{optional:!0,skipSelf:!0})||SD()});var xh=Symbol("InputSignalNode#UNSET"),zD=j(m({},ws),{transformFn:void 0,applyValueToInputSignal(e,t){Is(e,t)}});function Oh(e,t){let n=Object.create(zD);n.value=e,n.transformFn=t?.transform;function r(){if(ms(n),n.value===xh){let o=null;throw new y(-950,o)}return n.value}return r[Zr]=n,r}var GD=new v("");GD.__NG_ELEMENT_ID__=e=>{let t=Ce();if(t===null)throw new y(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new y(204,!1)};function Nh(e,t){return Oh(e,t)}function WD(e){return Oh(xh,e)}var Ph=(Nh.required=WD,Nh);var Wc=new v(""),qD=new v("");function vr(e){return!e.moduleRef}function ZD(e){let t=vr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(H);return n.run(()=>{vr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(ge),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),vr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Wc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Wc);s.add(i),e.moduleRef.onDestroy(()=>{ir(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return QD(r,n,()=>{let i=t.get(Bc);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(_i,yr);if(wh(s||yr),!t.get(qD,!0))return vr(e)?t.get($t):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(vr(e)){let c=t.get($t);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return YD?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var YD;function QD(e,t,n){try{let r=n();return Cn(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Si=null;function KD(e=[],t){return Re.create({name:t,providers:[{provide:Wn,useValue:"platform"},{provide:Wc,useValue:new Set([()=>Si=null])},...e]})}function JD(e=[]){if(Si)return Si;let t=KD(e);return Si=t,Dh(),XD(t),t}function XD(e){let t=e.get(yi,null);ce(e,()=>{t?.forEach(n=>n())})}var Kc=(()=>{class e{static __NG_ELEMENT_ID__=eE}return e})();function eE(e){return tE(Ce(),L(),(e&16)===16)}function tE(e,t,n){if(ct(e)&&!n){let r=we(e.index,t);return new ft(r,r)}else if(e.type&175){let r=t[he];return new ft(r,t)}return null}var qc=class{constructor(){}supports(t){return Ac(t)}create(t){return new Zc(t)}},nE=(e,t)=>t,Zc=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||nE}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Rh(r,o,i)?n:r,a=Rh(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,g=f+h;l<=g&&g<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Ac(t))throw new y(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,ih(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Yc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Mi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Mi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Yc=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Qc=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Mi=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Qc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Rh(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Ah(){return new Jc([new qc])}var Jc=(()=>{class e{factories;static \u0275prov=D({token:e,providedIn:"root",factory:Ah});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Ah()),deps:[[e,new qd,new Wd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new y(901,!1)}}return e})();function kh(e){F(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=JD(r),i=[zc({}),{provide:Tt,useExisting:Mh},dd,...n||[]],s=new lr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return ZD({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{F(9)}}var jh=null;function Ye(){return jh}function Xc(e){jh??=e}var Dr=class{},eu=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Bh),providedIn:"platform"})}return e})();var Bh=(()=>{class e extends eu{_location;_history;_doc=p(q);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ye().getBaseHref(this._doc)}onPopState(n){let r=Ye().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Ye().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Vh(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Fh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function pt(e){return e&&e[0]!=="?"?`?${e}`:e}var Ni=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p($h),providedIn:"root"})}return e})(),Uh=new v(""),$h=(()=>{class e extends Ni{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(q).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Vh(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+pt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+pt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+pt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(eu),C(Uh,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bn=(()=>{class e{_subject=new X;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=iE(Fh(Lh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+pt(r))}normalize(n){return e.stripTrailingSlash(oE(this._basePath,Lh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+pt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+pt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=pt;static joinWithSlash=Vh;static stripTrailingSlash=Fh;static \u0275fac=function(r){return new(r||e)(C(Ni))};static \u0275prov=D({token:e,factory:()=>rE(),providedIn:"root"})}return e})();function rE(){return new bn(C(Ni))}function oE(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Lh(e){return e.replace(/\/index.html$/,"")}function iE(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var tu=/\s+/,Hh=[],aE=(()=>{class e{_ngEl;_renderer;initialClasses=Hh;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(tu):Hh}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(tu):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(tu).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(Fe(gi),Fe(Rc))};static \u0275dir=Ut({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Ri=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Wh=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ri(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),zh(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);zh(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Fe(Vt),Fe(fr),Fe(Jc))};static \u0275dir=Ut({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function zh(e,t){e.context.$implicit=t.item}var cE=(()=>{class e{_viewContainer;_context=new Ai;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Gh(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Gh(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Fe(Vt),Fe(fr))};static \u0275dir=Ut({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ai=class{$implicit=null;ngIf=null};function Gh(e,t){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var qh=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=wn({type:e});static \u0275inj=_t({})}return e})();function nu(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Er=class{};var Zh="browser",uE="server";function Yh(e){return e===uE}var Pi=new v(""),su=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(Pi),C(H))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Ir=class{_doc;constructor(t){this._doc=t}manager},xi="ng-app-id";function Qh(e){for(let t of e)t.remove()}function Kh(e,t){let n=t.createElement("style");return n.textContent=e,n}function lE(e,t,n,r){let o=e.head?.querySelectorAll(`style[${xi}="${t}"],link[${xi}="${t}"]`);if(o)for(let i of o)i.removeAttribute(xi),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function ou(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var au=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Yh(i),lE(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Kh);r?.forEach(o=>this.addUsage(o,this.external,ou))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Qh(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Qh(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Kh(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,ou(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(xi,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(q),C(mi),C(vi,8),C(In))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),ru={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},cu=/%COMP%/g;var Xh="%COMP%",dE=`_nghost-${Xh}`,fE=`_ngcontent-${Xh}`,hE=!0,pE=new v("",{providedIn:"root",factory:()=>hE});function gE(e){return fE.replace(cu,e)}function mE(e){return dE.replace(cu,e)}function ep(e,t){return t.map(n=>n.replace(cu,e))}var uu=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new wr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof Oi?o.applyToHost(n):o instanceof Cr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case qe.Emulated:i=new Oi(c,u,r,this.appId,l,s,a,d,h);break;case qe.ShadowDom:return new iu(c,u,n,r,s,a,this.nonce,d,h);default:i=new Cr(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(su),C(au),C(mi),C(pE),C(q),C(In),C(H),C(vi),C(mr,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),wr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(ru[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Jh(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Jh(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new y(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=ru[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=ru[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(ht.DashCase|ht.Important)?t.style.setProperty(n,r,o&ht.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&ht.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Ye().getGlobalEventTarget(this.doc,t),!t))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function Jh(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var iu=class extends wr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=ep(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=ou(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Cr=class extends wr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?ep(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Oi=class extends Cr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=gE(l),this.hostAttr=mE(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var ki=class e extends Dr{supportsDOMEvents=!0;static makeCurrent(){Xc(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=yE();return n==null?null:vE(n)}resetBaseElement(){br=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return nu(document.cookie,t)}},br=null;function yE(){return br=br||document.head.querySelector("base"),br?br.getAttribute("href"):null}function vE(e){return new URL(e,document.baseURI).pathname}var DE=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),np=(()=>{class e extends Ir{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(q))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),tp=["alt","control","meta","shift"],EE={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},IE={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},rp=(()=>{class e extends Ir{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ye().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),tp.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=EE[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),tp.forEach(s=>{if(s!==o){let a=IE[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(q))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function wE(e,t){return kh(m({rootComponent:e},CE(t)))}function CE(e){return{appProviders:[...ME,...e?.providers??[]],platformProviders:SE}}function bE(){ki.makeCurrent()}function TE(){return new Ae}function _E(){return dc(document),document}var SE=[{provide:In,useValue:Zh},{provide:yi,useValue:bE,multi:!0},{provide:q,useFactory:_E}];var ME=[{provide:Wn,useValue:"root"},{provide:Ae,useFactory:TE},{provide:Pi,useClass:np,multi:!0,deps:[q]},{provide:Pi,useClass:rp,multi:!0,deps:[q]},uu,au,su,{provide:jt,useExisting:uu},{provide:Er,useClass:DE},[]];var du=class{};var zt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var fu=class{encodeKey(t){return op(t)}encodeValue(t){return op(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function NE(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var RE=/%(\d[a-f0-9])/gi,AE={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function op(e){return encodeURIComponent(e).replace(RE,(t,n)=>AE[n]??t)}function Fi(e){return`${e}`}var Qe=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new fu,t.fromString){if(t.fromObject)throw new y(2805,!1);this.map=NE(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Fi):[Fi(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Fi(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Fi(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var hu=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function xE(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function ip(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function sp(e){return typeof Blob<"u"&&e instanceof Blob}function ap(e){return typeof FormData<"u"&&e instanceof FormData}function OE(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var PE="X-Request-URL",cp="text/plain",up="application/json",iL=`${up}, ${cp}, */*`,Tn=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(xE(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new zt,this.context??=new hu,!this.params)this.params=new Qe,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||ip(this.body)||sp(this.body)||ap(this.body)||OE(this.body)?this.body:this.body instanceof Qe?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||ap(this.body)?null:sp(this.body)?this.body.type||null:ip(this.body)?null:typeof this.body=="string"?cp:this.body instanceof Qe?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?up:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.keepalive??this.keepalive,s=t.transferCache??this.transferCache,a=t.body!==void 0?t.body:this.body,c=t.withCredentials??this.withCredentials,u=t.reportProgress??this.reportProgress,l=t.headers||this.headers,d=t.params||this.params,h=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((f,g)=>f.set(g,t.setHeaders[g]),l)),t.setParams&&(d=Object.keys(t.setParams).reduce((f,g)=>f.set(g,t.setParams[g]),d)),new e(n,r,a,{params:d,headers:l,context:h,reportProgress:u,responseType:o,withCredentials:c,transferCache:s,keepalive:i})}},gu=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(gu||{}),pu=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new zt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}};var Li=class e extends pu{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=gu.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}};function lu(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive}}var kE=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Tn)i=n;else{let c;o.headers instanceof zt?c=o.headers:c=new zt(o.headers);let u;o.params&&(o.params instanceof Qe?u=o.params:u=new Qe({fromObject:o.params})),i=new Tn(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive})}let s=E(i).pipe(nt(c=>this.handler.handle(c)));if(n instanceof Tn||o.observe==="events")return s;let a=s.pipe(se(c=>c instanceof Li));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new y(2806,!1);return c.body}));case"blob":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new y(2807,!1);return c.body}));case"text":return a.pipe(_(c=>{if(c.body!==null&&typeof c.body!="string")throw new y(2808,!1);return c.body}));case"json":default:return a.pipe(_(c=>c.body))}case"response":return a;default:throw new y(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Qe().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,lu(o,r))}post(n,r,o={}){return this.request("POST",n,lu(o,r))}put(n,r,o={}){return this.request("PUT",n,lu(o,r))}static \u0275fac=function(r){return new(r||e)(C(du))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var sL=RegExp(`^${PE}:`,"m");var lp=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(q))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var T="primary",Br=Symbol("RouteTitle"),Eu=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function qt(e){return new Eu(e)}function vp(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function LE(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Le(e[n],t[n]))return!1;return!0}function Le(e,t){let n=e?Iu(e):void 0,r=t?Iu(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Dp(e[o],t[o]))return!1;return!0}function Iu(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Dp(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function Ep(e){return e.length>0?e[e.length-1]:null}function Xe(e){return xs(e)?e:Cn(e)?$(Promise.resolve(e)):E(e)}var jE={exact:wp,subset:Cp},Ip={exact:BE,subset:VE,ignored:()=>!0};function dp(e,t,n){return jE[n.paths](e.root,t.root,n.matrixParams)&&Ip[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function BE(e,t){return Le(e,t)}function wp(e,t,n){if(!Gt(e.segments,t.segments)||!Vi(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!wp(e.children[r],t.children[r],n))return!1;return!0}function VE(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Dp(e[n],t[n]))}function Cp(e,t,n){return bp(e,t,t.segments,n)}function bp(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Gt(o,n)||t.hasChildren()||!Vi(o,n,r))}else if(e.segments.length===n.length){if(!Gt(e.segments,n)||!Vi(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!Cp(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Gt(e.segments,o)||!Vi(e.segments,o,r)||!e.children[T]?!1:bp(e.children[T],t,i,r)}}function Vi(e,t,n){return t.every((r,o)=>Ip[n](e[o].parameters,r.parameters))}var Be=class{root;queryParams;fragment;_queryParamMap;constructor(t=new k([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=qt(this.queryParams),this._queryParamMap}toString(){return HE.serialize(this)}},k=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ui(this)}},gt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=qt(this.parameters),this._parameterMap}toString(){return _p(this)}};function UE(e,t){return Gt(e,t)&&e.every((n,r)=>Le(n.parameters,t[r].parameters))}function Gt(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function $E(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===T&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==T&&(n=n.concat(t(o,r)))}),n}var Vr=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new Zt,providedIn:"root"})}return e})(),Zt=class{parse(t){let n=new Cu(t);return new Be(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Tr(t.root,!0)}`,r=WE(t.queryParams),o=typeof t.fragment=="string"?`#${zE(t.fragment)}`:"";return`${n}${r}${o}`}},HE=new Zt;function Ui(e){return e.segments.map(t=>_p(t)).join("/")}function Tr(e,t){if(!e.hasChildren())return Ui(e);if(t){let n=e.children[T]?Tr(e.children[T],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==T&&r.push(`${o}:${Tr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=$E(e,(r,o)=>o===T?[Tr(e.children[T],!1)]:[`${o}:${Tr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[T]!=null?`${Ui(e)}/${n[0]}`:`${Ui(e)}/(${n.join("//")})`}}function Tp(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ji(e){return Tp(e).replace(/%3B/gi,";")}function zE(e){return encodeURI(e)}function wu(e){return Tp(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function $i(e){return decodeURIComponent(e)}function fp(e){return $i(e.replace(/\+/g,"%20"))}function _p(e){return`${wu(e.path)}${GE(e.parameters)}`}function GE(e){return Object.entries(e).map(([t,n])=>`;${wu(t)}=${wu(n)}`).join("")}function WE(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${ji(n)}=${ji(o)}`).join("&"):`${ji(n)}=${ji(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var qE=/^[^\/()?;#]+/;function mu(e){let t=e.match(qE);return t?t[0]:""}var ZE=/^[^\/()?;=#]+/;function YE(e){let t=e.match(ZE);return t?t[0]:""}var QE=/^[^=?&#]+/;function KE(e){let t=e.match(QE);return t?t[0]:""}var JE=/^[^&#]+/;function XE(e){let t=e.match(JE);return t?t[0]:""}var Cu=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new k([],{}):new k([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[T]=new k(t,n)),r}parseSegment(){let t=mu(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(t),new gt($i(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=YE(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=mu(this.remaining);o&&(r=o,this.capture(r))}t[$i(n)]=$i(r)}parseQueryParam(t){let n=KE(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=XE(this.remaining);s&&(r=s,this.capture(r))}let o=fp(n),i=fp(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=mu(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=T);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[T]:new k([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new y(4011,!1)}};function Sp(e){return e.segments.length>0?new k([],{[T]:e}):e}function Mp(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Mp(o);if(r===T&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new k(e.segments,t);return eI(n)}function eI(e){if(e.numberOfChildren===1&&e.children[T]){let t=e.children[T];return new k(e.segments.concat(t.segments),t.children)}return e}function Rn(e){return e instanceof Be}function Np(e,t,n=null,r=null){let o=Rp(e);return Ap(o,t,n,r)}function Rp(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new k(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Sp(r);return t??o}function Ap(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return yu(o,o,o,n,r);let i=tI(t);if(i.toRoot())return yu(o,o,new k([],{}),n,r);let s=nI(i,o,e),a=s.processChildren?Sr(s.segmentGroup,s.index,i.commands):Op(s.segmentGroup,s.index,i.commands);return yu(o,s.segmentGroup,a,n,r)}function Hi(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Rr(e){return typeof e=="object"&&e!=null&&e.outlets}function yu(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=xp(e,t,n);let a=Sp(Mp(s));return new Be(a,i,o)}function xp(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=xp(i,t,n)}),new k(e.segments,r)}var zi=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Hi(r[0]))throw new y(4003,!1);let o=r.find(Rr);if(o&&o!==Ep(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function tI(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new zi(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new zi(n,t,r)}var Mn=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function nI(e,t,n){if(e.isAbsolute)return new Mn(t,!0,0);if(!n)return new Mn(t,!1,NaN);if(n.parent===null)return new Mn(n,!0,0);let r=Hi(e.commands[0])?0:1,o=n.segments.length-1+r;return rI(n,o,e.numberOfDoubleDots)}function rI(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new Mn(r,!1,o-i)}function oI(e){return Rr(e[0])?e[0].outlets:{[T]:e}}function Op(e,t,n){if(e??=new k([],{}),e.segments.length===0&&e.hasChildren())return Sr(e,t,n);let r=iI(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new k(e.segments.slice(0,r.pathIndex),{});return i.children[T]=new k(e.segments.slice(r.pathIndex),e.children),Sr(i,0,o)}else return r.match&&o.length===0?new k(e.segments,{}):r.match&&!e.hasChildren()?bu(e,t,n):r.match?Sr(e,0,o):bu(e,t,n)}function Sr(e,t,n){if(n.length===0)return new k(e.segments,{});{let r=oI(n),o={};if(Object.keys(r).some(i=>i!==T)&&e.children[T]&&e.numberOfChildren===1&&e.children[T].segments.length===0){let i=Sr(e.children[T],t,n);return new k(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Op(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new k(e.segments,o)}}function iI(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Rr(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!pp(c,u,s))return i;r+=2}else{if(!pp(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function bu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Rr(i)){let c=sI(i.outlets);return new k(r,c)}if(o===0&&Hi(n[0])){let c=e.segments[t];r.push(new gt(c.path,hp(n[0]))),o++;continue}let s=Rr(i)?i.outlets[T]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Hi(a)?(r.push(new gt(s,hp(a))),o+=2):(r.push(new gt(s,{})),o++)}return new k(r,{})}function sI(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=bu(new k([],{}),0,r))}),t}function hp(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function pp(e,t,n){return e==n.path&&Le(t,n.parameters)}var Mr="imperative",J=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(J||{}),ye=class{id;url;constructor(t,n){this.id=t,this.url=n}},Yt=class extends ye{type=J.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ke=class extends ye{urlAfterRedirects;type=J.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},re=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(re||{}),Ar=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Ar||{}),je=class extends ye{reason;code;type=J.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Je=class extends ye{reason;code;type=J.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},An=class extends ye{error;target;type=J.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},xr=class extends ye{urlAfterRedirects;state;type=J.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Gi=class extends ye{urlAfterRedirects;state;type=J.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Wi=class extends ye{urlAfterRedirects;state;shouldActivate;type=J.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},qi=class extends ye{urlAfterRedirects;state;type=J.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Zi=class extends ye{urlAfterRedirects;state;type=J.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Yi=class{route;type=J.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Qi=class{route;type=J.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ki=class{snapshot;type=J.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ji=class{snapshot;type=J.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Xi=class{snapshot;type=J.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},es=class{snapshot;type=J.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Or=class{},xn=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function aI(e){return!(e instanceof Or)&&!(e instanceof xn)}function cI(e,t){return e.providers&&!e._injector&&(e._injector=gr(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Ne(e){return e.outlet||T}function uI(e,t){let n=e.filter(r=>Ne(r)===t);return n.push(...e.filter(r=>Ne(r)!==t)),n}function Ur(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var ts=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ur(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new kn(this.rootInjector)}},kn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new ts(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(C(Y))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ns=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Tu(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Tu(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=_u(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return _u(t,this._root).map(n=>n.value)}};function Tu(e,t){if(e===t.value)return t;for(let n of t.children){let r=Tu(e,n);if(r)return r}return null}function _u(e,t){if(e===t.value)return[t];for(let n of t.children){let r=_u(e,n);if(r.length)return r.unshift(t),r}return[]}var me=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Sn(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Pr=class extends ns{snapshot;constructor(t,n){super(t),this.snapshot=n,Pu(this,t)}toString(){return this.snapshot.toString()}};function Pp(e){let t=lI(e),n=new ee([new gt("",{})]),r=new ee({}),o=new ee({}),i=new ee({}),s=new ee(""),a=new mt(n,r,i,s,o,T,e,t.root);return a.snapshot=t.root,new Pr(new me(a,[]),t)}function lI(e){let t={},n={},r={},o="",i=new Wt([],t,r,o,n,T,e,null,{});return new kr("",new me(i,[]))}var mt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(_(u=>u[Br]))??E(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(_(t=>qt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(_(t=>qt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function rs(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:m(m({},t.params),e.params),data:m(m({},t.data),e.data),resolve:m(m(m(m({},e.data),t.data),o?.data),e._resolvedData)}:r={params:m({},e.params),data:m({},e.data),resolve:m(m({},e.data),e._resolvedData??{})},o&&Fp(o)&&(r.resolve[Br]=o.title),r}var Wt=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Br]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=qt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=qt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},kr=class extends ns{url;constructor(t,n){super(n),this.url=t,Pu(this,n)}toString(){return kp(this._root)}};function Pu(e,t){t.value._routerState=e,t.children.forEach(n=>Pu(e,n))}function kp(e){let t=e.children.length>0?` { ${e.children.map(kp).join(", ")} } `:"";return`${e.value}${t}`}function vu(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Le(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Le(t.params,n.params)||e.paramsSubject.next(n.params),LE(t.url,n.url)||e.urlSubject.next(n.url),Le(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Su(e,t){let n=Le(e.params,t.params)&&UE(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Su(e.parent,t.parent))}function Fp(e){return typeof e.title=="string"||e.title===null}var Lp=new v(""),ku=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=T;activateEvents=new ne;deactivateEvents=new ne;attachEvents=new ne;detachEvents=new ne;routerOutletData=Ph(void 0);parentContexts=p(kn);location=p(Vt);changeDetector=p(Kc);inputBinder=p(as,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Mu(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Ut({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[fi]})}return e})(),Mu=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===mt?this.route:t===kn?this.childContexts:t===Lp?this.outletData:this.parent.get(t,n)}},as=new v("");var Fu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=xc({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Ti(0,"router-outlet")},dependencies:[ku],encapsulation:2})}return e})();function Lu(e){let t=e.children&&e.children.map(Lu),n=t?j(m({},e),{children:t}):m({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==T&&(n.component=Fu),n}function dI(e,t,n){let r=Fr(e,t._root,n?n._root:void 0);return new Pr(r,t)}function Fr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=fI(e,t,n);return new me(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Fr(e,a)),s}}let r=hI(t.value),o=t.children.map(i=>Fr(e,i));return new me(r,o)}}function fI(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Fr(e,r,o);return Fr(e,r)})}function hI(e){return new mt(new ee(e.url),new ee(e.params),new ee(e.queryParams),new ee(e.fragment),new ee(e.data),e.outlet,e.component,e)}var On=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},jp="ngNavigationCancelingError";function os(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=Rn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Bp(!1,re.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Bp(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[jp]=!0,n.cancellationCode=t,n}function pI(e){return Vp(e)&&Rn(e.url)}function Vp(e){return!!e&&e[jp]}var gI=(e,t,n,r)=>_(o=>(new Nu(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Nu=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),vu(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Sn(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Sn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Sn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Sn(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new es(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Ji(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(vu(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),vu(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},is=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Nn=class{component;route;constructor(t,n){this.component=t,this.route=n}};function mI(e,t,n){let r=e._root,o=t?t._root:null;return _r(r,o,n,[r.value])}function yI(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Fn(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Qs(e)?e:t.get(e):r}function _r(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Sn(t);return e.children.forEach(s=>{vI(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Nr(a,n.getContext(s),o)),o}function vI(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=DI(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new is(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?_r(e,t,a?a.children:null,r,o):_r(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Nn(a.outlet.component,s))}else s&&Nr(t,a,o),o.canActivateChecks.push(new is(r)),i.component?_r(e,null,a?a.children:null,r,o):_r(e,null,n,r,o);return o}function DI(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Gt(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Gt(e.url,t.url)||!Le(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Su(e,t)||!Le(e.queryParams,t.queryParams);case"paramsChange":default:return!Su(e,t)}}function Nr(e,t,n){let r=Sn(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Nr(s,t.children.getContext(i),n):Nr(s,null,n):Nr(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Nn(t.outlet.component,o)):n.canDeactivateChecks.push(new Nn(null,o)):n.canDeactivateChecks.push(new Nn(null,o))}function $r(e){return typeof e=="function"}function EI(e){return typeof e=="boolean"}function II(e){return e&&$r(e.canLoad)}function wI(e){return e&&$r(e.canActivate)}function CI(e){return e&&$r(e.canActivateChild)}function bI(e){return e&&$r(e.canDeactivate)}function TI(e){return e&&$r(e.canMatch)}function Up(e){return e instanceof Ue||e?.name==="EmptyError"}var Bi=Symbol("INITIAL_VALUE");function Pn(){return le(e=>wo(e.map(t=>t.pipe($e(1),ks(Bi)))).pipe(_(t=>{for(let n of t)if(n!==!0){if(n===Bi)return Bi;if(n===!1||_I(n))return n}return!0}),se(t=>t!==Bi),$e(1)))}function _I(e){return Rn(e)||e instanceof On}function SI(e,t){return G(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?E(j(m({},n),{guardsResult:!0})):MI(s,r,o,e).pipe(G(a=>a&&EI(a)?NI(r,i,e,t):E(a)),_(a=>j(m({},n),{guardsResult:a})))})}function MI(e,t,n,r){return $(e).pipe(G(o=>PI(o.component,o.route,n,t,r)),He(o=>o!==!0,!0))}function NI(e,t,n,r){return $(t).pipe(nt(o=>sn(AI(o.route.parent,r),RI(o.route,r),OI(e,o.path,n),xI(e,o.route,n))),He(o=>o!==!0,!0))}function RI(e,t){return e!==null&&t&&t(new Xi(e)),E(!0)}function AI(e,t){return e!==null&&t&&t(new Ki(e)),E(!0)}function xI(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return E(!0);let o=r.map(i=>Vn(()=>{let s=Ur(t)??n,a=Fn(i,s),c=wI(a)?a.canActivate(t,e):ce(s,()=>a(t,e));return Xe(c).pipe(He())}));return E(o).pipe(Pn())}function OI(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>yI(s)).filter(s=>s!==null).map(s=>Vn(()=>{let a=s.guards.map(c=>{let u=Ur(s.node)??n,l=Fn(c,u),d=CI(l)?l.canActivateChild(r,e):ce(u,()=>l(r,e));return Xe(d).pipe(He())});return E(a).pipe(Pn())}));return E(i).pipe(Pn())}function PI(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return E(!0);let s=i.map(a=>{let c=Ur(t)??o,u=Fn(a,c),l=bI(u)?u.canDeactivate(e,t,n,r):ce(c,()=>u(e,t,n,r));return Xe(l).pipe(He())});return E(s).pipe(Pn())}function kI(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return E(!0);let i=o.map(s=>{let a=Fn(s,e),c=II(a)?a.canLoad(t,n):ce(e,()=>a(t,n));return Xe(c)});return E(i).pipe(Pn(),$p(r))}function $p(e){return Ms(Z(t=>{if(typeof t!="boolean")throw os(e,t)}),_(t=>t===!0))}function FI(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return E(!0);let i=o.map(s=>{let a=Fn(s,e),c=TI(a)?a.canMatch(t,n):ce(e,()=>a(t,n));return Xe(c)});return E(i).pipe(Pn(),$p(r))}var Lr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},jr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function _n(e){return on(new Lr(e))}function LI(e){return on(new y(4e3,!1))}function jI(e){return on(Bp(!1,re.GuardRejected))}var Ru=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return E(r);if(o.numberOfChildren>1||!o.children[T])return LI(`${t.redirectTo}`);o=o.children[T]}}applyRedirectCommands(t,n,r,o,i){return BI(n,o,i).pipe(_(s=>{if(s instanceof Be)throw new jr(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,r);if(s[0]==="/")throw new jr(a);return a}))}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Be(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new k(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}};function BI(e,t,n){if(typeof e=="string")return E(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=t;return Xe(ce(n,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var Au={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function VI(e,t,n,r,o){let i=Hp(e,t,n);return i.matched?(r=cI(t,r),FI(r,t,n,o).pipe(_(s=>s===!0?i:m({},Au)))):E(i)}function Hp(e,t,n){if(t.path==="**")return UI(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?m({},Au):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||vp)(n,e,t);if(!o)return m({},Au);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?m(m({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function UI(e){return{matched:!0,parameters:e.length>0?Ep(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function gp(e,t,n,r){return n.length>0&&zI(e,n,r)?{segmentGroup:new k(t,HI(r,new k(n,e.children))),slicedSegments:[]}:n.length===0&&GI(e,n,r)?{segmentGroup:new k(e.segments,$I(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new k(e.segments,e.children),slicedSegments:n}}function $I(e,t,n,r){let o={};for(let i of n)if(cs(e,t,i)&&!r[Ne(i)]){let s=new k([],{});o[Ne(i)]=s}return m(m({},r),o)}function HI(e,t){let n={};n[T]=t;for(let r of e)if(r.path===""&&Ne(r)!==T){let o=new k([],{});n[Ne(r)]=o}return n}function zI(e,t,n){return n.some(r=>cs(e,t,r)&&Ne(r)!==T)}function GI(e,t,n){return n.some(r=>cs(e,t,r))}function cs(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function WI(e,t,n){return t.length===0&&!e.children[n]}var xu=class{};function qI(e,t,n,r,o,i,s="emptyOnly"){return new Ou(e,t,n,r,o,s,i).recognize()}var ZI=31,Ou=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Ru(this.urlSerializer,this.urlTree)}noMatchError(t){return new y(4002,`'${t.segmentGroup}'`)}recognize(){let t=gp(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(_(({children:n,rootSnapshot:r})=>{let o=new me(r,n),i=new kr("",o),s=Np(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Wt([],Object.freeze({}),Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),T,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,T,n).pipe(_(r=>({children:r,rootSnapshot:n})),tt(r=>{if(r instanceof jr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Lr?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(_(s=>s instanceof me?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return $(i).pipe(nt(s=>{let a=r.children[s],c=uI(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Ps((s,a)=>(s.push(...a),s)),rt(null),Os(),G(s=>{if(s===null)return _n(r);let a=zp(s);return YI(a),E(a)}))}processSegment(t,n,r,o,i,s,a){return $(n).pipe(nt(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(tt(u=>{if(u instanceof Lr)return E(null);throw u}))),He(c=>!!c),tt(c=>{if(Up(c))return WI(r,o,i)?E(new xu):_n(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Ne(r)!==s&&(s===T||!cs(o,i,r))?_n(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):_n(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=Hp(n,o,i);if(!c)return _n(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>ZI&&(this.allowRedirects=!1));let f=new Wt(i,u,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,mp(o),Ne(o),o.component??o._loadedComponent??null,o,yp(o)),g=rs(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(g.params),f.data=Object.freeze(g.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t).pipe(le(P=>this.applyRedirects.lineralizeSegments(o,P)),G(P=>this.processSegment(t,r,n,P.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=VI(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(le(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(le(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,g=new Wt(h,d,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,mp(r),Ne(r),r.component??r._loadedComponent??null,r,yp(r)),I=rs(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(I.params),g.data=Object.freeze(I.data);let{segmentGroup:P,slicedSegments:x}=gp(n,h,f,u);if(x.length===0&&P.hasChildren())return this.processChildren(l,u,P,g).pipe(_(Gr=>new me(g,Gr)));if(u.length===0&&x.length===0)return E(new me(g,[]));let yt=Ne(r)===i;return this.processSegment(l,u,P,x,yt?T:i,!0,g).pipe(_(Gr=>new me(g,Gr instanceof me?[Gr]:[])))}))):_n(n)))}getChildConfig(t,n,r){return n.children?E({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?E({routes:n._loadedRoutes,injector:n._loadedInjector}):kI(t,n,r,this.urlSerializer).pipe(G(o=>o?this.configLoader.loadChildren(t,n).pipe(Z(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):jI(n))):E({routes:[],injector:t})}};function YI(e){e.sort((t,n)=>t.value.outlet===T?-1:n.value.outlet===T?1:t.value.outlet.localeCompare(n.value.outlet))}function QI(e){let t=e.value.routeConfig;return t&&t.path===""}function zp(e){let t=[],n=new Set;for(let r of e){if(!QI(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=zp(r.children);t.push(new me(r.value,o))}return t.filter(r=>!n.has(r))}function mp(e){return e.data||{}}function yp(e){return e.resolve||{}}function KI(e,t,n,r,o,i){return G(s=>qI(e,t,n,r,s.extractedUrl,o,i).pipe(_(({state:a,tree:c})=>j(m({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function JI(e,t){return G(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return E(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Gp(c))s.add(u);let a=0;return $(s).pipe(nt(c=>i.has(c)?XI(c,r,e,t):(c.data=rs(c,c.parent,e).resolve,E(void 0))),Z(()=>a++),cn(1),G(c=>a===s.size?E(n):oe))})}function Gp(e){let t=e.children.map(n=>Gp(n)).flat();return[e,...t]}function XI(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Fp(o)&&(i[Br]=o.title),Vn(()=>(e.data=rs(e,e.parent,n).resolve,ew(i,e,t,r).pipe(_(s=>(e._resolvedData=s,e.data=m(m({},e.data),s),null)))))}function ew(e,t,n,r){let o=Iu(e);if(o.length===0)return E({});let i={};return $(o).pipe(G(s=>tw(e[s],t,n,r).pipe(He(),Z(a=>{if(a instanceof On)throw os(new Zt,a);i[s]=a}))),cn(1),_(()=>i),tt(s=>Up(s)?oe:on(s)))}function tw(e,t,n,r){let o=Ur(t)??r,i=Fn(e,o),s=i.resolve?i.resolve(t,n):ce(o,()=>i(t,n));return Xe(s)}function Du(e){return le(t=>{let n=e(t);return n?$(n).pipe(_(()=>t)):E(t)})}var ju=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===T);return r}getResolvedTitleForRoute(n){return n.data[Br]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Wp),providedIn:"root"})}return e})(),Wp=(()=>{class e extends ju{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(C(lp))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Hr=new v("",{providedIn:"root",factory:()=>({})}),zr=new v(""),qp=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Hc);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return E(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Xe(n.loadComponent()).pipe(_(Yp),Z(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),an(()=>{this.componentLoaders.delete(n)})),o=new rn(r,()=>new X).pipe(nn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return E({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Zp(r,this.compiler,n,this.onLoadEndListener).pipe(an(()=>{this.childrenLoaders.delete(r)})),s=new rn(i,()=>new X).pipe(nn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Zp(e,t,n,r){return Xe(e.loadChildren()).pipe(_(Yp),G(o=>o instanceof Ci||Array.isArray(o)?E(o):$(t.compileModuleAsync(o))),_(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(zr,[],{optional:!0,self:!0}).flat()),{routes:s.map(Lu),injector:i}}))}function nw(e){return e&&typeof e=="object"&&"default"in e}function Yp(e){return nw(e)?e.default:e}var us=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(rw),providedIn:"root"})}return e})(),rw=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Qp=new v("");var Kp=new v(""),Jp=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new X;transitionAbortWithErrorSubject=new X;configLoader=p(qp);environmentInjector=p(Y);destroyRef=p(lt);urlSerializer=p(Vr);rootContexts=p(kn);location=p(bn);inputBindingEnabled=p(as,{optional:!0})!==null;titleStrategy=p(ju);options=p(Hr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(us);createViewTransition=p(Qp,{optional:!0});navigationErrorHandler=p(Kp,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>E(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Yi(o)),r=o=>this.events.next(new Qi(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(m({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(n){return this.transitions=new ee(null),this.transitions.pipe(se(r=>r!==null),le(r=>{let o=!1;return E(r).pipe(le(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",re.SupersededByNewNavigation),oe;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?j(m({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new Je(i.id,this.urlSerializer.serialize(i.rawUrl),c,Ar.IgnoredSameUrlNavigation)),i.resolve(!1),oe}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return E(i).pipe(le(c=>(this.events.next(new Yt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?oe:Promise.resolve(c))),KI(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Z(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=j(m({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new xr(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Yt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let g=Pp(this.rootComponentType).snapshot;return this.currentTransition=r=j(m({},i),{targetSnapshot:g,urlAfterRedirects:u,extras:j(m({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,E(r)}else{let c="";return this.events.next(new Je(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Ar.IgnoredByUrlHandlingStrategy)),i.resolve(!1),oe}}),Z(i=>{let s=new Gi(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),_(i=>(this.currentTransition=r=j(m({},i),{guards:mI(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),SI(this.environmentInjector,i=>this.events.next(i)),Z(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw os(this.urlSerializer,i.guardsResult);let s=new Wi(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),se(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",re.GuardRejected),!1)),Du(i=>{if(i.guards.canActivateChecks.length!==0)return E(i).pipe(Z(s=>{let a=new qi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),le(s=>{let a=!1;return E(s).pipe(JI(this.paramsInheritanceStrategy,this.environmentInjector),Z({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",re.NoDataFromResolver)}}))}),Z(s=>{let a=new Zi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Du(i=>{let s=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(Z(u=>{a.component=u}),_(()=>{})));for(let u of a.children)c.push(...s(u));return c};return wo(s(i.targetSnapshot.root)).pipe(rt(null),$e(1))}),Du(()=>this.afterPreactivation()),le(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?$(a).pipe(_(()=>r)):E(r)}),_(i=>{let s=dI(n.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=j(m({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),Z(()=>{this.events.next(new Or)}),gI(this.rootContexts,n.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),$e(1),bo(new R(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(se(()=>!o&&!r.targetRouterState),Z(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",re.Aborted)}))),Z({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ke(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),bo(this.transitionAbortWithErrorSubject.pipe(Z(i=>{throw i}))),an(()=>{o||this.cancelNavigationTransition(r,"",re.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),tt(i=>{if(this.destroyed)return r.resolve(!1),oe;if(o=!0,Vp(i))this.events.next(new je(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),pI(i)?this.events.next(new xn(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new An(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=ce(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof On){let{message:c,cancellationCode:u}=os(this.urlSerializer,a);this.events.next(new je(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new xn(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return oe}))}))}cancelNavigationTransition(n,r,o){let i=new je(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ow(e){return e!==Mr}var Xp=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(iw),providedIn:"root"})}return e})(),ss=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},iw=(()=>{class e extends ss{static \u0275fac=(()=>{let n;return function(o){return(n||(n=hi(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),eg=(()=>{class e{urlSerializer=p(Vr);options=p(Hr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(bn);urlHandlingStrategy=p(us);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Be;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Be?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Pp(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(sw),providedIn:"root"})}return e})(),sw=(()=>{class e extends eg{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Yt?this.updateStateMemento():n instanceof Je?this.commitTransition(r):n instanceof xr?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Or?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof je&&n.code!==re.SupersededByNewNavigation&&n.code!==re.Redirect?this.restoreHistory(r):n instanceof An?this.restoreHistory(r,!0):n instanceof Ke&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=m(m({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=m(m({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=hi(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Bu(e,t){e.events.pipe(se(n=>n instanceof Ke||n instanceof je||n instanceof An||n instanceof Je),_(n=>n instanceof Ke||n instanceof Je?0:(n instanceof je?n.code===re.Redirect||n.code===re.SupersededByNewNavigation:!1)?2:1),se(n=>n!==2),$e(1)).subscribe(()=>{t()})}var aw={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},cw={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ls=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Fc);stateManager=p(eg);options=p(Hr,{optional:!0})||{};pendingTasks=p(dt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(Jp);urlSerializer=p(Vr);location=p(bn);urlHandlingStrategy=p(us);injector=p(Y);_events=new X;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Xp);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(zr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(as,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new V;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof je&&r.code!==re.Redirect&&r.code!==re.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ke)this.navigated=!0;else if(r instanceof xn){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=m({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||ow(o.source)},s);this.scheduleNavigation(a,Mr,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}aI(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Mr,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=m({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i).catch(c=>{this.injector.get(ge)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Lu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=m(m({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=Rp(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Ap(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=Rn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Mr,null,r)}navigate(n,r={skipLocationChange:!1}){return uw(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=m({},aw):r===!1?o=m({},cw):o=r,Rn(n))return dp(this.currentUrlTree,n,o);let i=this.parseUrl(n);return dp(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Bu(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function uw(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new y(4008,!1)}var dw=new v("");function fw(e,...t){return st([{provide:zr,multi:!0,useValue:e},[],{provide:mt,useFactory:hw,deps:[ls]},{provide:bi,multi:!0,useFactory:pw},t.map(n=>n.\u0275providers)])}function hw(e){return e.routerState.root}function pw(){let e=p(Re);return t=>{let n=e.get($t);if(t!==n.components[0])return;let r=e.get(ls),o=e.get(gw);e.get(mw)===1&&r.initialNavigation(),e.get(yw,null,{optional:!0})?.setUpPreloading(),e.get(dw,null,{optional:!0})?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var gw=new v("",{factory:()=>new X}),mw=new v("",{providedIn:"root",factory:()=>1});var yw=new v("");export{m as a,j as b,_ as c,xg as d,le as e,D as f,C as g,ad as h,cd as i,am as j,Km as k,yy as l,Fe as m,xc as n,hh as o,lD as p,dD as q,Ih as r,Vc as s,Uc as t,Ti as u,Ch as v,yD as w,vD as x,bh as y,$c as z,Th as A,wD as B,CD as C,TD as D,aE as E,Wh as F,cE as G,qh as H,wE as I,kE as J,ku as K,ls as L,fw as M};
