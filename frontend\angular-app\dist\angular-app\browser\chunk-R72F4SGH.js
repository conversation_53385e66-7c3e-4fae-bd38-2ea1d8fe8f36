import{a as k}from"./chunk-ZJ25V4KC.js";import{E as _,F as C,G as I,H as O,J as h,M as P,f as u,g as x,l as o,m as g,n as v,o as s,r as c,s as r,t as i,v as m,w as p,x as a,y as f,z as b}from"./chunk-73GOI2TA.js";var l=class e{constructor(t){this.http=t}apiBaseUrl=k.apiBaseUrl;getIngredients(){return this.http.get(`${this.apiBaseUrl}/ingredients?limit=100`)}static \u0275fac=function(n){return new(n||e)(x(h))};static \u0275prov=u({token:e,factory:e.\u0275fac,providedIn:"root"})};function E(e,t){e&1&&(r(0,"div",10),a(1,"\u{1F96C} Loading ingredients..."),i())}function D(e,t){if(e&1&&(r(0,"div",11),a(1),i()),e&2){let n=p();o(),f(n.error)}}function F(e,t){e&1&&(r(0,"div",12),a(1," No ingredients found. Use the admin panel to add some ingredients! "),i())}function T(e,t){if(e&1&&(r(0,"div",22),a(1),i()),e&2){let n=p().$implicit;o(),b(" ",n.description," ")}}function N(e,t){if(e&1&&(r(0,"div",17)(1,"div",18)(2,"div",19),a(3),i(),s(4,T,2,1,"div",20),i(),r(5,"div",21),a(6),i()()),e&2){let n=t.$implicit;c("ngClass","fodmap-"+(n.fodmap_level||"unknown").toLowerCase()),o(3),f(n.name),o(),c("ngIf",n.description),o(),c("ngClass","fodmap-"+(n.fodmap_level||"unknown").toLowerCase()),o(),b(" ",n.fodmap_level||"UNKNOWN"," ")}}function j(e,t){if(e&1&&(r(0,"div",13)(1,"h2",14),a(2,"Ingredients & FODMAP Levels"),i(),r(3,"div",15),s(4,N,7,5,"div",16),i()()),e&2){let n=p();o(4),c("ngForOf",n.ingredients)}}var M=class e{constructor(t,n){this.ingredientService=t;this.router=n}ingredients=[];loading=!0;error="";ngOnInit(){this.ingredientService.getIngredients().subscribe({next:t=>{console.log("Ingredients response:",t),this.ingredients=t.data||[],this.loading=!1},error:t=>{console.error("Ingredients error:",t),this.error="Failed to load ingredients: "+t.message,this.loading=!1}})}goToRecipes(){this.router.navigate(["/"])}openAdminPanel(){window.open("admin.html","_blank")}static \u0275fac=function(n){return new(n||e)(g(l),g(P))};static \u0275cmp=v({type:e,selectors:[["app-ingredients"]],decls:13,vars:4,consts:[[1,"ingredients-container"],[1,"header-content"],[1,"logo"],[1,"nav-buttons"],[1,"nav-button",3,"click"],[1,"nav-button","admin-button",3,"click"],["class","loading",4,"ngIf"],["class","error",4,"ngIf"],["class","no-ingredients",4,"ngIf"],["class","content",4,"ngIf"],[1,"loading"],[1,"error"],[1,"no-ingredients"],[1,"content"],[1,"section-title"],[1,"ingredients-grid"],["class","ingredient-card",3,"ngClass",4,"ngFor","ngForOf"],[1,"ingredient-card",3,"ngClass"],[1,"ingredient-info"],[1,"ingredient-name"],["class","ingredient-description",4,"ngIf"],[1,"fodmap-badge",3,"ngClass"],[1,"ingredient-description"]],template:function(n,d){n&1&&(r(0,"div",0)(1,"div",1)(2,"h1",2),a(3,"\u{1F37D}\uFE0F FODMAP Recipes"),i(),r(4,"div",3)(5,"button",4),m("click",function(){return d.goToRecipes()}),a(6,"\u{1F4D6} Recipes"),i(),r(7,"button",5),m("click",function(){return d.openAdminPanel()}),a(8,"\u2699\uFE0F Admin Panel"),i()()(),s(9,E,2,0,"div",6)(10,D,2,1,"div",7)(11,F,2,0,"div",8)(12,j,5,1,"div",9),i()),n&2&&(o(9),c("ngIf",d.loading),o(),c("ngIf",d.error),o(),c("ngIf",!d.loading&&!d.error&&d.ingredients.length===0),o(),c("ngIf",!d.loading&&!d.error&&d.ingredients.length>0))},dependencies:[O,_,C,I],styles:[".ingredients-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:2rem;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;margin-bottom:2rem;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);padding:1rem 2rem;border-radius:20px;box-shadow:0 10px 30px #0000001a}.logo[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;background:linear-gradient(45deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;margin:0}.nav-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.nav-button[_ngcontent-%COMP%]{padding:.75rem 1.5rem;border:none;border-radius:25px;cursor:pointer;font-weight:600;text-decoration:none;display:inline-block;transition:all .3s ease;font-size:.9rem;background:linear-gradient(45deg,#667eea,#764ba2);color:#fff}.nav-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 5px 15px #667eea66}.admin-button[_ngcontent-%COMP%]{background:linear-gradient(45deg,#28a745,#20c997)!important}.admin-button[_ngcontent-%COMP%]:hover{box-shadow:0 5px 15px #28a74566!important}.loading[_ngcontent-%COMP%], .error[_ngcontent-%COMP%], .no-ingredients[_ngcontent-%COMP%]{text-align:center;padding:2rem;font-size:1.2rem;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px;box-shadow:0 10px 30px #0000001a}.error[_ngcontent-%COMP%]{color:#dc3545;background:#f8d7da;border:1px solid #f5c6cb}.content[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);padding:2rem;border-radius:20px;box-shadow:0 10px 30px #0000001a}.section-title[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:1.5rem;color:#2c3e50;text-align:center}.ingredients-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1rem}.ingredient-card[_ngcontent-%COMP%]{background:#fff;padding:1rem;border-radius:10px;box-shadow:0 2px 10px #0000000d;display:flex;justify-content:space-between;align-items:center;transition:all .3s ease;border-left:4px solid transparent}.ingredient-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 5px 20px #0000001a}.ingredient-info[_ngcontent-%COMP%]{flex:1}.ingredient-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;font-size:1.1rem;margin-bottom:.25rem}.ingredient-description[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem;line-height:1.4}.fodmap-badge[_ngcontent-%COMP%]{padding:.5rem 1rem;border-radius:20px;font-size:.8rem;font-weight:700;text-transform:uppercase;white-space:nowrap}.fodmap-low[_ngcontent-%COMP%]{border-left-color:#28a745}.fodmap-low[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.fodmap-moderate[_ngcontent-%COMP%]{border-left-color:#ffc107}.fodmap-moderate[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.fodmap-high[_ngcontent-%COMP%]{border-left-color:#dc3545}.fodmap-high[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.fodmap-unknown[_ngcontent-%COMP%]{border-left-color:#6c757d}.fodmap-unknown[_ngcontent-%COMP%]   .fodmap-badge[_ngcontent-%COMP%]{background:#e2e3e5;color:#495057}"]})};export{M as IngredientsComponent};
