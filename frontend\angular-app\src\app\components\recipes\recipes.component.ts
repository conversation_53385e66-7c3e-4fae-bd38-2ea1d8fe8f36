import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { RecipeService } from '../../services/recipe.service';
import { CategoryService } from '../../services/category.service';

@Component({
  selector: 'app-recipes',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './recipes.component.html',
  styleUrls: ['./recipes.component.css']
})
export class RecipesComponent implements OnInit {
  recipes: any[] = [];
  categories: any[] = [];
  groupedRecipes: { [key: string]: any[] } = {};
  loading = true;
  error = '';

  constructor(
    private recipeService: RecipeService,
    private categoryService: CategoryService,
    private router: Router
  ) {}

  ngOnInit() {
    console.log('RecipesComponent ngOnInit called');
    this.loadData();
  }

  loadData() {
    // Load both recipes and categories
    Promise.all([
      this.recipeService.getRecipes().toPromise(),
      this.categoryService.getCategories().toPromise()
    ]).then(([recipesRes, categoriesRes]) => {
      console.log('Recipes response:', recipesRes);
      console.log('Categories response:', categoriesRes);
      
      this.recipes = recipesRes?.data || [];
      this.categories = categoriesRes?.data || [];
      
      this.groupRecipesByCategory();
      this.loading = false;
    }).catch(err => {
      console.error('Error loading data:', err);
      this.error = 'Failed to load data: ' + err.message;
      this.loading = false;
    });
  }

  groupRecipesByCategory() {
    this.groupedRecipes = {};
    
    // Group recipes by category
    this.recipes.forEach(recipe => {
      const categoryName = recipe.category_name || 'Uncategorized';
      if (!this.groupedRecipes[categoryName]) {
        this.groupedRecipes[categoryName] = [];
      }
      this.groupedRecipes[categoryName].push(recipe);
    });
  }

  getCategoryKeys(): string[] {
    return Object.keys(this.groupedRecipes);
  }

  goToIngredients() {
    this.router.navigate(['/ingredients']);
  }

  openAdminPanel() {
    window.open('admin.html', '_blank');
  }
}
